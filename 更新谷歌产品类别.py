import requests
import json
import os
from datetime import datetime
import base64
import urllib3
import logging

# 禁用SSL证书验证警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# 加载配置文件
with open("config.json", "r", encoding="utf-8") as f:
    config = json.load(f)

# Shopify店铺详情
SHOP_NAME = config["shopify"]["shop_name"]  # 不带myshopify.com的版本
API_KEY = config["shopify"]["api_key"]
API_PASSWORD = config["shopify"]["api_password"]

# Shopify API URL
API_URL = f"https://{SHOP_NAME}.myshopify.com/admin/api/2023-10/graphql.json"

# 设置请求头
headers = {"Content-Type": "application/json", "X-Shopify-Access-Token": API_PASSWORD}


def get_product_ids(page_size=100):
    """获取店铺中的所有产品ID列表，使用分页"""
    all_products = []
    cursor = None
    has_next_page = True
    page = 1

    while has_next_page:
        # 构建带有分页的GraphQL查询
        if cursor:
            query = f"""
            {{
              products(first: {page_size}, after: "{cursor}") {{
                pageInfo {{
                  hasNextPage
                }}
                edges {{
                  cursor
                  node {{
                    id
                    title
                  }}
                }}
              }}
            }}
            """
        else:
            query = f"""
            {{
              products(first: {page_size}) {{
                pageInfo {{
                  hasNextPage
                }}
                edges {{
                  cursor
                  node {{
                    id
                    title
                  }}
                }}
              }}
            }}
            """

        print(f"正在获取第 {page} 页产品（每页 {page_size} 个）...")
        logging.info(f"正在获取第 {page} 页产品（每页 {page_size} 个）...")

        try:
            response = requests.post(
                API_URL,
                headers=headers,
                json={"query": query},
                verify=False,  # 禁用SSL证书验证
                timeout=30,  # 设置超时时间
            )

            if response.status_code == 200:
                result = response.json()
                products_data = result.get("data", {}).get("products", {})
                edges = products_data.get("edges", [])

                # 获取当前页的产品
                page_products = [
                    (edge["node"]["id"], edge["node"]["title"]) for edge in edges
                ]
                all_products.extend(page_products)

                print(f"已获取 {len(page_products)} 个产品，总计: {len(all_products)}")

                # 检查是否有下一页
                has_next_page = products_data.get("pageInfo", {}).get(
                    "hasNextPage", False
                )

                # 如果有下一页，获取最后一个产品的游标
                if has_next_page and edges:
                    cursor = edges[-1]["cursor"]
                    page += 1
                else:
                    has_next_page = False
            else:
                print(f"获取产品失败: {response.status_code}")
                print(response.text)
                logging.error(
                    f"获取产品失败: {response.status_code}, {response.text[:200]}"
                )
                break

        except Exception as e:
            print(f"获取产品时发生错误: {str(e)}")
            logging.error(f"获取产品时发生错误: {str(e)}")
            break

    return all_products


def update_google_product_category(product_id, category_id="3006"):
    """更新产品的Google Product Category为指定的ID"""
    # GraphQL变量
    variables = {
        "input": {
            "id": product_id,
            "metafields": [
                {
                    "namespace": "google",
                    "key": "google_product_category",
                    "type": "single_line_text_field",
                    "value": str(category_id),
                }
            ],
        }
    }

    # GraphQL查询
    mutation = """
    mutation updateProductMetafields($input: ProductInput!) {
      productUpdate(input: $input) {
        product {
          id
          title
          metafields(first: 10) {
            edges {
              node {
                namespace
                key
                value
              }
            }
          }
        }
        userErrors {
          message
          field
        }
      }
    }
    """

    # 发送请求
    try:
        response = requests.post(
            API_URL,
            headers=headers,
            json={"query": mutation, "variables": variables},
            verify=False,  # 禁用SSL证书验证
            timeout=30,  # 设置超时时间
        )
    except requests.exceptions.RequestException as e:
        logging.error(f"更新产品 {product_id} 时请求异常: {str(e)}")
        print(f"更新产品时请求异常: {str(e)}")
        return False

    if response.status_code == 200:
        result = response.json()
        user_errors = (
            result.get("data", {}).get("productUpdate", {}).get("userErrors", [])
        )

        if user_errors:
            print(f"更新产品 {product_id} 失败:")
            for error in user_errors:
                print(f"  - {error['message']}")
            return False
        else:
            return True
    else:
        print(f"API请求失败: {response.status_code}")
        print(response.text)
        return False


def setup_logging():
    """设置日志"""
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s [%(levelname)s] %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )


def main():
    # 设置日志
    setup_logging()

    # 显示当前使用的店铺信息
    print(f"当前使用的店铺: {SHOP_NAME}")
    print(f"API URL: {API_URL}")
    print("SSL证书验证已禁用")
    print("-" * 50)

    # 设置默认每页产品数量为250（Shopify API的最大值）
    page_size = 250

    # 获取产品ID列表
    print(f"正在获取所有产品（每页 {page_size} 个）...")
    try:
        products = get_product_ids(page_size=page_size)
    except Exception as e:
        logging.error(f"获取产品列表时出错: {str(e)}")
        print(f"获取产品列表时出错: {str(e)}")
        return

    if not products:
        print("没有找到产品，或获取产品列表失败")
        return

    print(f"找到 {len(products)} 个产品")

    # 默认更新所有产品
    print(f"将更新所有 {len(products)} 个产品的Google Product Category为3006")
    update_all = True

    if update_all:
        # 更新所有产品
        success_count = 0
        total_count = len(products)

        # 默认启用批量处理模式
        batch_mode = True

        if batch_mode:
            # 设置默认批处理大小为20
            batch_size = 20
            print(f"已启用批量处理模式，每批处理 {batch_size} 个产品")

            # 批量处理产品
            for i in range(0, total_count, batch_size):
                batch = products[i : i + batch_size]
                print(
                    f"\n正在处理批次 {i//batch_size + 1}/{(total_count + batch_size - 1)//batch_size}，共 {len(batch)} 个产品"
                )

                batch_success = 0
                for j, (product_id, product_title) in enumerate(batch):
                    print(f"[{i+j+1}/{total_count}] 正在更新产品: {product_title}")
                    if update_google_product_category(product_id):
                        success_count += 1
                        batch_success += 1
                        print(f"  ✓ 更新成功")
                    else:
                        print(f"  ✗ 更新失败")

                print(
                    f"批次 {i//batch_size + 1} 完成: {batch_success}/{len(batch)} 个产品成功更新"
                )

                # 如果不是最后一批，自动继续，但显示进度信息
                if i + batch_size < total_count:
                    remaining = total_count - (i + batch_size)
                    print(
                        f"已完成 {i + batch_size}/{total_count}，剩余 {remaining} 个产品"
                    )
                    print("自动继续处理下一批...")
                    # 暂停1秒，让用户有机会查看进度
                    import time

                    time.sleep(1)
        else:
            # 逐个处理产品
            for i, (product_id, product_title) in enumerate(products):
                print(f"[{i+1}/{total_count}] 正在更新产品: {product_title}")
                if update_google_product_category(product_id):
                    success_count += 1
                    print(f"  ✓ 更新成功")
                else:
                    print(f"  ✗ 更新失败")

        print(f"\n更新完成: {success_count}/{total_count} 个产品成功更新")
    # 移除单个产品选择部分，默认处理所有产品


if __name__ == "__main__":
    main()
