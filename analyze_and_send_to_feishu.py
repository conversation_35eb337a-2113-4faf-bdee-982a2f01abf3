import os
import requests
import json
import urllib3
import re
import html2text
import logging
import time
import traceback
import concurrent.futures
from datetime import datetime
from chromadb.utils import embedding_functions
from bs4 import BeautifulSoup


# 加载配置文件
def load_config():
    """加载配置文件"""
    try:
        with open("config.json", "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception as e:
        logging.error(f"加载配置文件失败: {str(e)}")
        # 返回默认配置
        return {
            "models": {
                "deepseek": {
                    "name": "DeepSeek V3",
                    "api_url": "https://api.siliconflow.cn",
                    "api_key": "sk-bgspngbbihsidpsdhaxsrxcopipavguuivzxzdciebgfaclg",
                    "model_id": "deepseek-ai/DeepSeek-V3",
                    "max_tokens": 4096,
                    "temperature": 0.1,
                }
            },
            "active_model": "deepseek",
            "shopify": {
                "shop_name": "9uii4g-sf",
                "api_key": "7413850cc553ff28f23a72b6208befcd",
                "api_password": "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",
                "rate_limit": 2,
            },
        }


# 加载配置
CONFIG = load_config()

# 从配置中获取Shopify信息
SHOP_NAME = CONFIG["shopify"]["shop_name"]
# 获取自定义域名（如果有）
CUSTOM_DOMAIN = CONFIG["shopify"].get("custom_domain", "")

# 从get_shopify_products_info导入函数
from get_shopify_products_info import get_auth_headers, get_all_products


# 设置日志配置
def setup_logging():
    """设置日志配置"""
    # 创建logs目录（如果不存在）
    if not os.path.exists("logs"):
        os.makedirs("logs")

    # 生成日志文件名，包含时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f"logs/policy_pilot_{timestamp}.log"

    # 配置日志格式
    log_format = "%(asctime)s [%(levelname)s] %(message)s"
    date_format = "%Y-%m-%d %H:%M:%S"

    # 设置root logger
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        datefmt=date_format,
        handlers=[
            logging.FileHandler(log_filename, encoding="utf-8"),
            logging.StreamHandler(),  # 同时输出到控制台
        ],
    )

    logging.info(f"日志已初始化，日志文件：{log_filename}")
    return log_filename


# 初始化日志
log_filename = setup_logging()

# 禁用不安全请求警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# ===== 配置部分 =====
# ChromaDB 配置
DB_DIR = "chroma_db"  # ChromaDB数据目录

# 从配置文件获取并发处理配置
MAX_WORKERS = CONFIG["analysis"].get("max_workers", 5)  # 最大工作线程数


# AI模型API调用函数
def call_ai_model_api(
    prompt, max_tokens=None, temperature=None, top_p=0.9, timeout=60, max_retries=3
):
    """
    调用AI模型API进行文本生成，支持多种模型

    参数:
        prompt (str): 提示词
        max_tokens (int): 最大生成token数，如果为None则使用配置文件中的值
        temperature (float): 温度参数，控制随机性，如果为None则使用配置文件中的值
        top_p (float): 核采样参数
        timeout (int): 请求超时时间（秒）
        max_retries (int): 最大重试次数

    返回:
        str: 生成的文本
    """
    # 获取当前激活的模型配置
    active_model_key = CONFIG.get("active_model", "deepseek")
    model_config = CONFIG["models"].get(active_model_key)

    if not model_config:
        logging.error(f"未找到模型配置: {active_model_key}，使用默认DeepSeek模型")
        model_config = CONFIG["models"].get("deepseek")

    # 获取模型配置
    api_url = model_config.get("api_url")
    api_key = model_config.get("api_key")
    model_id = model_config.get("model_id")

    # 如果未指定参数，则使用配置文件中的值
    if max_tokens is None:
        max_tokens = model_config.get("max_tokens", 2048)
    if temperature is None:
        temperature = model_config.get("temperature", 0.1)

    logging.info(f"使用模型: {model_config.get('name')} ({model_id})")

    # 根据不同模型构建不同的请求
    if active_model_key == "deepseek":
        return call_deepseek_api(
            api_url,
            api_key,
            model_id,
            prompt,
            max_tokens,
            temperature,
            top_p,
            timeout,
            max_retries,
        )
    elif active_model_key == "gemini":
        return call_gemini_api(
            api_url,
            api_key,
            model_id,
            prompt,
            max_tokens,
            temperature,
            top_p,
            timeout,
            max_retries,
        )
    elif active_model_key == "qwen":
        return call_qwen_api(
            api_url,
            api_key,
            model_id,
            prompt,
            max_tokens,
            temperature,
            top_p,
            timeout,
            max_retries,
        )
    else:
        logging.error(f"不支持的模型类型: {active_model_key}")
        return f"不支持的模型类型: {active_model_key}"


# DeepSeek API调用函数
def call_deepseek_api(
    api_url,
    api_key,
    model_id,
    prompt,
    max_tokens,
    temperature,
    top_p,
    timeout,
    max_retries,
):
    """调用DeepSeek API"""
    headers = {"Content-Type": "application/json", "Authorization": f"Bearer {api_key}"}

    data = {
        "model": model_id,
        "messages": [{"role": "user", "content": prompt}],
        "max_tokens": max_tokens,
        "temperature": temperature,
        "top_p": top_p,
    }

    logging.debug(f"准备调用DeepSeek API，模型: {model_id}, max_tokens: {max_tokens}")

    # 实现重试机制
    for attempt in range(max_retries):
        try:
            response = requests.post(
                f"{api_url}/v1/chat/completions",
                headers=headers,
                json=data,
                timeout=timeout,
            )

            # 检查响应状态
            if response.status_code == 200:
                response_json = response.json()
                if "choices" in response_json and len(response_json["choices"]) > 0:
                    content = response_json["choices"][0]["message"]["content"]
                    logging.debug(f"成功获取DeepSeek API响应，长度: {len(content)}")
                    return content
                else:
                    logging.warning(
                        f"DeepSeek API响应中没有choices字段: {response_json}"
                    )
                    if attempt == max_retries - 1:
                        return f"DeepSeek API响应格式错误，没有choices字段"
            else:
                logging.warning(
                    f"DeepSeek API请求失败(尝试 {attempt+1}/{max_retries}): HTTP {response.status_code}, 响应: {response.text[:200]}"
                )
                if attempt == max_retries - 1:
                    return f"DeepSeek API请求失败: HTTP {response.status_code}"

        except requests.exceptions.Timeout:
            logging.warning(f"DeepSeek API请求超时(尝试 {attempt+1}/{max_retries})")
            if attempt == max_retries - 1:
                return f"DeepSeek API请求在{timeout}秒内超时，已重试{max_retries}次"

        except requests.exceptions.RequestException as e:
            logging.warning(
                f"DeepSeek API请求异常(尝试 {attempt+1}/{max_retries}): {str(e)}"
            )
            if attempt == max_retries - 1:
                return f"DeepSeek API请求异常: {str(e)}"

        # 如果不是最后一次尝试，等待一段时间后重试
        if attempt < max_retries - 1:
            wait_time = 2**attempt  # 指数退避策略：1, 2, 4, 8...秒
            logging.info(f"等待{wait_time}秒后重试...")
            time.sleep(wait_time)

    return "DeepSeek API调用失败，超过最大重试次数"


# Gemini API调用函数
def call_gemini_api(
    api_url,
    api_key,
    model_id,
    prompt,
    max_tokens,
    temperature,
    top_p,
    timeout,
    max_retries,
):
    """调用Gemini API"""
    headers = {"Content-Type": "application/json"}

    # Gemini API使用不同的参数名称
    data = {
        "contents": [{"parts": [{"text": prompt}]}],
        "generationConfig": {
            "maxOutputTokens": max_tokens,
            "temperature": temperature,
            "topP": top_p,
        },
    }

    logging.debug(f"准备调用Gemini API，模型: {model_id}, max_tokens: {max_tokens}")

    # 构建URL，包含API密钥
    url = f"{api_url}/v1beta/models/{model_id}:generateContent?key={api_key}"

    # 实现重试机制
    for attempt in range(max_retries):
        try:
            response = requests.post(url, headers=headers, json=data, timeout=timeout)

            if response.status_code == 200:
                result = response.json()
                if "candidates" in result and len(result["candidates"]) > 0:
                    candidate = result["candidates"][0]

                    # 检查是否有content字段
                    if "content" not in candidate:
                        logging.warning(f"Gemini API响应中没有content字段: {candidate}")
                        if attempt == max_retries - 1:
                            return f"Gemini API响应格式错误，没有content字段"
                        continue

                    content_obj = candidate["content"]

                    # 检查是否有parts字段
                    if "parts" not in content_obj:
                        logging.warning(f"Gemini API响应中没有parts字段: {content_obj}")
                        if attempt == max_retries - 1:
                            return f"Gemini API响应格式错误，没有parts字段"
                        continue

                    # 检查parts是否为空
                    if not content_obj["parts"] or len(content_obj["parts"]) == 0:
                        logging.warning(f"Gemini API响应中parts字段为空: {content_obj}")
                        if attempt == max_retries - 1:
                            return f"Gemini API响应格式错误，parts字段为空"
                        continue

                    # 检查第一个part是否有text字段
                    first_part = content_obj["parts"][0]
                    if "text" not in first_part:
                        logging.warning(f"Gemini API响应中第一个part没有text字段: {first_part}")
                        if attempt == max_retries - 1:
                            return f"Gemini API响应格式错误，第一个part没有text字段"
                        continue

                    content = first_part["text"]
                    logging.debug(f"Gemini API调用成功，返回内容长度: {len(content)}")
                    return content
                else:
                    logging.warning(f"Gemini API返回格式异常: {result}")
                    if attempt == max_retries - 1:
                        return f"Gemini API响应格式错误，没有candidates字段"
            else:
                logging.warning(
                    f"Gemini API调用失败 (尝试 {attempt+1}/{max_retries}): HTTP {response.status_code}, {response.text[:200]}"
                )
                if attempt == max_retries - 1:
                    return f"Gemini API请求失败: HTTP {response.status_code}"

        except requests.exceptions.Timeout:
            logging.warning(f"Gemini API请求超时(尝试 {attempt+1}/{max_retries})")
            if attempt == max_retries - 1:
                return f"Gemini API请求在{timeout}秒内超时，已重试{max_retries}次"

        except requests.exceptions.RequestException as e:
            logging.warning(
                f"Gemini API请求异常(尝试 {attempt+1}/{max_retries}): {str(e)}"
            )
            if attempt == max_retries - 1:
                return f"Gemini API请求异常: {str(e)}"

        # 如果不是最后一次尝试，等待一段时间后重试
        if attempt < max_retries - 1:
            wait_time = 2**attempt  # 指数退避策略：1, 2, 4, 8...秒
            logging.info(f"等待{wait_time}秒后重试...")
            time.sleep(wait_time)

    return "Gemini API调用失败，超过最大重试次数"


# Qwen API调用函数
def call_qwen_api(
    api_url,
    api_key,
    model_id,
    prompt,
    max_tokens,
    temperature,
    top_p,
    timeout,
    max_retries,
):
    """调用Qwen API (通过DashScope兼容模式)"""
    headers = {"Content-Type": "application/json", "Authorization": f"Bearer {api_key}"}

    # 根据阿里云灵积模型服务文档构建请求数据
    # 参考: https://help.aliyun.com/document_detail/2870973.html
    data = {
        "model": model_id,
        "messages": [{"role": "user", "content": prompt}],
        "max_tokens": max_tokens,
        "temperature": temperature,
        "top_p": top_p,
        "stream": True,  # 启用流式调用
        "stream_options": {"include_usage": True},  # 包含token使用量
        "parameters": {
            "enable_thinking": False,  # 禁用思考功能
            "thinking_budget": 0,  # 思考预算，0表示禁用思考功能
        },
    }

    logging.debug(
        f"准备调用Qwen API，模型: {model_id}, max_tokens: {max_tokens}，流式调用，已启用思考功能，思考预算: 0.5"
    )

    # 实现重试机制
    for attempt in range(max_retries):
        try:
            # 检查API URL是否已经包含完整路径
            if api_url.endswith("/"):
                api_endpoint = f"{api_url}chat/completions"
            else:
                api_endpoint = f"{api_url}/chat/completions"

            logging.debug(f"发送请求到: {api_endpoint}")
            response = requests.post(
                api_endpoint, headers=headers, json=data, timeout=timeout
            )

            # 检查响应状态
            if response.status_code == 200:
                # 处理流式响应
                full_content = ""
                thinking_content = ""
                is_thinking = False

                try:
                    for line in response.iter_lines():
                        if not line:
                            continue

                        # 移除 "data: " 前缀
                        if line.startswith(b"data: "):
                            line = line[6:]

                        if line.strip() == b"[DONE]":
                            break

                        try:
                            chunk = json.loads(line)

                            # 检查是否是思考部分
                            if "thinking" in chunk:
                                is_thinking = True
                                thinking_content += chunk["thinking"]
                                continue

                            # 检查是否有choices字段
                            if "choices" in chunk and chunk["choices"]:
                                # 检查是否有delta字段
                                if "delta" in chunk["choices"][0]:
                                    delta_content = chunk["choices"][0]["delta"].get(
                                        "content", ""
                                    )
                                    if delta_content:
                                        full_content += delta_content
                                # 检查是否有message字段
                                elif "message" in chunk["choices"][0]:
                                    message_content = chunk["choices"][0][
                                        "message"
                                    ].get("content", "")
                                    if message_content:
                                        full_content += message_content
                        except json.JSONDecodeError:
                            logging.warning(f"无法解析JSON行: {line}")
                            continue

                    # 记录思考内容（如果有）
                    if is_thinking and thinking_content:
                        logging.debug(f"获取到思考内容，长度: {len(thinking_content)}")
                        log_ai_output("qwen_thinking", thinking_content)

                    logging.debug(
                        f"成功获取Qwen API流式响应，长度: {len(full_content)}"
                    )
                    return full_content
                except Exception as e:
                    logging.error(f"处理流式响应时出错: {str(e)}")
                    if attempt == max_retries - 1:
                        return f"处理Qwen API流式响应时出错: {str(e)}"
            elif response.status_code == 404:
                # 404错误通常表示API端点不正确
                error_msg = f"Qwen API请求失败(尝试 {attempt+1}/{max_retries}): HTTP 404 Not Found，API端点可能不正确: {api_endpoint}"
                logging.warning(error_msg)
                logging.warning(f"响应内容: {response.text[:500]}")
                if attempt == max_retries - 1:
                    return f"Qwen API请求失败: HTTP 404 Not Found，API端点可能不正确"
            elif response.status_code == 401 or response.status_code == 403:
                # 认证错误
                error_msg = f"Qwen API请求失败(尝试 {attempt+1}/{max_retries}): HTTP {response.status_code}，认证失败，请检查API密钥"
                logging.warning(error_msg)
                logging.warning(f"响应内容: {response.text[:500]}")
                if attempt == max_retries - 1:
                    return f"Qwen API请求失败: HTTP {response.status_code}，认证失败，请检查API密钥"
            elif response.status_code == 429:
                # 限流错误
                error_msg = f"Qwen API请求被限流(尝试 {attempt+1}/{max_retries}): HTTP 429 Too Many Requests"
                logging.warning(error_msg)
                logging.warning(f"响应内容: {response.text[:500]}")
                # 限流错误需要更长的等待时间
                wait_time = 5 * (2**attempt)  # 更长的指数退避: 5, 10, 20...秒
                logging.info(f"遇到限流，等待{wait_time}秒后重试...")
                time.sleep(wait_time)
                if attempt == max_retries - 1:
                    return f"Qwen API请求被限流: HTTP 429 Too Many Requests，已重试{max_retries}次"
            elif response.status_code == 400:
                # 400错误，可能是参数问题
                error_msg = f"Qwen API请求失败(尝试 {attempt+1}/{max_retries}): HTTP 400 Bad Request"
                logging.warning(error_msg)
                logging.warning(f"响应内容: {response.text[:500]}")

                # 检查是否是enable_thinking参数错误
                if "enable_thinking" in response.text:
                    logging.warning("检测到enable_thinking参数错误，尝试修改请求")
                    # 根据阿里云灵积模型服务文档，调整流式调用参数
                    data = {
                        "model": model_id,
                        "messages": [{"role": "user", "content": prompt}],
                        "max_tokens": max_tokens,
                        "temperature": temperature,
                        "top_p": top_p,
                        "stream": True,  # 确保使用流式调用
                        "parameters": {"enable_thinking": True},  # 确保启用思考功能
                    }
                    logging.info("已调整请求数据，确保使用流式调用和思考功能，重试请求")
                    continue

                if attempt == max_retries - 1:
                    return f"Qwen API请求失败: HTTP 400 Bad Request"
            else:
                # 其他错误
                logging.warning(
                    f"Qwen API请求失败(尝试 {attempt+1}/{max_retries}): HTTP {response.status_code}"
                )
                logging.warning(f"响应内容: {response.text[:500]}")
                if attempt == max_retries - 1:
                    return f"Qwen API请求失败: HTTP {response.status_code}"

        except requests.exceptions.Timeout:
            logging.warning(f"Qwen API请求超时(尝试 {attempt+1}/{max_retries})")
            if attempt == max_retries - 1:
                return f"Qwen API请求在{timeout}秒内超时，已重试{max_retries}次"

        except requests.exceptions.RequestException as e:
            logging.warning(
                f"Qwen API请求异常(尝试 {attempt+1}/{max_retries}): {str(e)}"
            )
            if attempt == max_retries - 1:
                return f"Qwen API请求异常: {str(e)}"

        # 如果不是最后一次尝试，等待一段时间后重试
        if attempt < max_retries - 1:
            wait_time = 2**attempt  # 指数退避策略：1, 2, 4, 8...秒
            logging.info(f"等待{wait_time}秒后重试...")
            time.sleep(wait_time)

    return "Qwen API调用失败，超过最大重试次数"


# 为了向后兼容，保留原函数名
def call_siliconflow_api(
    prompt, max_tokens=2048, temperature=0.1, top_p=0.9, timeout=60, max_retries=3
):
    """向后兼容的函数，调用当前配置的AI模型"""
    return call_ai_model_api(
        prompt, max_tokens, temperature, top_p, timeout, max_retries
    )


# 记录AI输出到日志文件
def log_ai_output(prefix, content):
    """
    记录AI输出到日志文件，便于调试和分析

    参数:
        prefix (str): 日志文件前缀
        content (str): 要记录的内容
    """
    try:
        # 创建logs/ai_outputs目录（如果不存在）
        output_dir = "logs/ai_outputs"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        # 生成文件名，包含时间戳
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{output_dir}/{prefix}_{timestamp}.txt"

        # 写入内容到文件
        with open(filename, "w", encoding="utf-8") as f:
            f.write(content)

        logging.debug(f"AI输出已记录到文件: {filename}")
    except Exception as e:
        logging.warning(f"记录AI输出失败: {str(e)}")
        # 不抛出异常，确保主流程不受影响


# 飞书API配置
FEISHU_APP_ID = "cli_a5a12cc72c78500c"
FEISHU_APP_SECRET = "WBP6rHyvPjeLe6qdPr84Texj1h2uc3Cw"
# 飞书电子表格ID
SPREADSHEET_TOKEN = "Xq4LsFeBhh9afotkOswcLr2tnqe"
# 工作表ID (可以从URL或API获取)
PRODUCT_SHEET_ID = "7d8ce1"
POLICY_SHEET_ID = "FAWERs"

logging.info("加载配置完成")

# ChromaDB数据目录
DB_DIR = "chroma_db"


# ===== 数据库相关函数 =====
def initialize_chroma_db(reset=False):
    """
    初始化ChromaDB客户端和集合

    参数:
        reset (bool): 是否重置集合，如果为True则删除现有集合并重新创建

    返回:
        Collection: ChromaDB集合对象
    """
    logging.info("初始化ChromaDB...")
    try:
        from chromadb import PersistentClient
        import os
        import sys

        # 导入自定义嵌入函数模块
        from embedding_utils import get_embedding_function

        # 确保数据目录存在
        os.makedirs(DB_DIR, exist_ok=True)

        # 初始化嵌入函数
        global _cached_embedding_function
        if (
            "_cached_embedding_function" not in globals()
            or _cached_embedding_function is None
        ):
            try:
                # 使用自定义嵌入函数模块，尝试使用CUDA
                # 从配置文件中获取是否强制使用CUDA的设置
                force_cuda = CONFIG.get("analysis", {}).get("force_cuda", False)
                device = CONFIG.get("analysis", {}).get("embedding_device", "auto")

                if force_cuda:
                    logging.info("配置设置为强制使用CUDA进行嵌入计算")

                embedding_function = get_embedding_function(
                    model_name="paraphrase-multilingual-mpnet-base-v2",
                    device=device,  # 使用配置中的设备设置
                    force_cuda=force_cuda,  # 是否强制使用CUDA
                )
                # 缓存嵌入函数以避免重复初始化
                _cached_embedding_function = embedding_function
                logging.info("成功初始化嵌入函数")
            except Exception as e:
                logging.error(f"初始化嵌入函数失败: {str(e)}")
                raise
        else:
            logging.info("使用缓存的嵌入函数")
            embedding_function = _cached_embedding_function

        # 初始化客户端
        client = PersistentClient(path=DB_DIR)

        # 如果需要重置集合
        if reset:
            try:
                client.delete_collection(name="google_merchant_policies")
                logging.info("成功删除现有集合: google_merchant_policies")
            except Exception as e:
                logging.warning(f"删除集合失败或集合不存在: {str(e)}")

        # 使用get_or_create方法，避免集合已存在的错误
        try:
            # 尝试使用get_or_create_collection方法
            if hasattr(client, "get_or_create_collection"):
                collection = client.get_or_create_collection(
                    name="google_merchant_policies",
                    embedding_function=embedding_function,
                )
                logging.info(
                    f"ChromaDB初始化成功，连接到集合：google_merchant_policies"
                )
                return collection
            else:
                # 如果方法不存在，先尝试获取，如果不存在再创建
                try:
                    collection = client.get_collection(
                        name="google_merchant_policies",
                        embedding_function=embedding_function,
                    )
                    logging.info("成功获取现有集合")
                    return collection
                except Exception:
                    collection = client.create_collection(
                        name="google_merchant_policies",
                        embedding_function=embedding_function,
                    )
                    logging.info("成功创建新集合")
                    return collection
        except Exception as e:
            logging.error(f"获取或创建集合失败: {str(e)}")

            # 尝试直接获取集合（不使用embedding_function）
            try:
                collection = client.get_collection(name="google_merchant_policies")
                # 手动设置嵌入函数
                if hasattr(collection, "_embedding_function"):
                    collection._embedding_function = embedding_function
                logging.info("成功获取现有集合并手动设置嵌入函数")
                return collection
            except Exception as e2:
                logging.error(f"获取现有集合失败: {str(e2)}")
                raise

    except Exception as e:
        logging.error(f"ChromaDB初始化失败: {str(e)}")
        raise


# ===== 并发处理函数 =====
def process_product_async(product, index, total):
    """
    异步处理单个产品的分析

    参数:
        product (dict): 产品信息
        index (int): 产品索引
        total (int): 总产品数

    返回:
        dict: 产品分析结果
    """
    try:
        logging.info(f"开始分析产品 {index+1}/{total}: {product['title']}")
        print(f"正在分析产品 {index+1}/{total}: {product['title']}")

        # 查询相关政策
        relevant_policies = query_policy_db_for_product(product)

        # 使用DeepSeek分析
        analysis = analyze_product_with_deepseek(product, relevant_policies)

        # 提取分析数据
        product_data = extract_product_analysis_data(product, analysis)

        # 获取风险级别
        risk_level = product_data["gmc_risk"]

        # 记录风险级别
        logging.info(f"完成产品分析: {product['title']}, 风险级别: {risk_level}")

        # 根据风险级别显示不同的消息
        if risk_level.lower() in ["中", "高"]:
            print(f"✓ 完成产品分析: {product['title']} - 风险级别: {risk_level}")
        else:
            print(
                f"✓ 完成产品分析: {product['title']} - 风险级别: {risk_level} (低风险)"
            )

        return product_data
    except Exception as e:
        error_msg = f"产品 {product['title']} 分析失败: {str(e)}"
        logging.error(error_msg)
        logging.error(traceback.format_exc())
        print(f"× {error_msg}")

        # 返回一个带有错误信息的默认结果
        return {
            "product_id": product.get("id", "unknown"),
            "title": product.get("title", "unknown"),
            "description": "分析失败",
            "link": f"https://{SHOP_NAME}.myshopify.com/products/{product.get('handle', '')}",
            "price": "",
            "inventory": 0,
            "product_type": product.get("product_type", ""),
            "special_categories": ", ".join(product.get("special_categories", [])),
            "gmc_risk": "未知",
            "fix_suggestion": f"分析失败: {str(e)}",
            "issues": [],
            "issues_count": 0,
            "issues_summary": f"分析过程中发生错误: {str(e)}",
        }


def analyze_products_in_parallel(detailed_products, timeout_per_product=300):
    """
    并行分析多个产品，提高处理速度，并过滤出中/高风险产品

    参数:
        detailed_products (list): 产品列表
        timeout_per_product (int): 每个产品分析的最大超时时间（秒）

    返回:
        list: 中/高风险产品分析结果列表
    """
    all_product_analysis_data = []  # 所有产品的分析结果
    medium_high_risk_products = []  # 只包含中/高风险的产品
    total = len(detailed_products)
    completed_count = 0
    error_count = 0
    skipped_count = 0  # 跳过的低风险产品数量

    logging.info(
        f"开始并行分析{total}个产品，最大工作线程数: {MAX_WORKERS}，每个产品超时时间: {timeout_per_product}秒"
    )
    print(
        f"开始并行分析{total}个产品，最大工作线程数: {MAX_WORKERS}，每个产品超时时间: {timeout_per_product}秒"
    )
    print(f"注意: 只会显示和处理中/高风险产品，低风险产品将被跳过")

    # 使用线程池并行处理
    with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        # 创建任务
        future_to_product = {}

        # 分批提交任务，避免一次性提交过多任务
        batch_size = min(MAX_WORKERS * 2, total)  # 每批次处理的产品数量
        for batch_start in range(0, total, batch_size):
            batch_end = min(batch_start + batch_size, total)
            logging.info(f"提交批次任务 {batch_start+1}-{batch_end}/{total}")

            for i in range(batch_start, batch_end):
                product = detailed_products[i]
                future = executor.submit(process_product_async, product, i, total)
                future_to_product[future] = (product, i)

            # 处理当前批次的结果
            for future in concurrent.futures.as_completed(future_to_product.copy()):
                product, index = future_to_product[future]
                try:
                    # 添加超时处理
                    product_data = future.result(timeout=timeout_per_product)

                    # 检查风险级别，只保留中/高风险产品
                    risk_level = product_data.get("gmc_risk", "").strip().lower()

                    # 将所有产品添加到完整列表
                    all_product_analysis_data.append(product_data)

                    # 只将中/高风险产品添加到过滤后的列表
                    if risk_level in ["中", "高"]:
                        medium_high_risk_products.append(product_data)
                        logging.info(
                            f"产品 '{product['title']}' 风险级别: {risk_level}，已添加到结果列表"
                        )
                        print(
                            f"✓ 完成产品分析: {product['title']} - 风险级别: {risk_level}"
                        )
                    else:
                        skipped_count += 1
                        logging.info(
                            f"产品 '{product['title']}' 风险级别: {risk_level}，已跳过（低风险）"
                        )
                        print(
                            f"✓ 完成产品分析: {product['title']} - 风险级别: {risk_level}（已跳过，低风险）"
                        )

                    completed_count += 1
                    logging.info(
                        f"收集产品分析结果 ({index+1}/{total}): {product['title']} - 进度: {completed_count}/{total}"
                    )

                    # 从待处理列表中移除已完成的任务
                    future_to_product.pop(future, None)

                except concurrent.futures.TimeoutError:
                    error_msg = f"分析产品 {product['title']} 超时（超过{timeout_per_product}秒）"
                    logging.error(error_msg)
                    print(f"⚠ {error_msg}")
                    error_count += 1

                    # 取消超时的任务
                    future.cancel()
                    future_to_product.pop(future, None)

                    # 创建一个默认的分析结果（默认为中风险）
                    default_data = create_default_product_analysis(
                        product, f"分析超时: 超过{timeout_per_product}秒"
                    )
                    all_product_analysis_data.append(default_data)
                    medium_high_risk_products.append(
                        default_data
                    )  # 默认为中风险，所以添加到结果列表

                except Exception as e:
                    error_msg = (
                        f"获取产品 {product['title']} 的分析结果时出错: {str(e)}"
                    )
                    logging.error(error_msg)
                    logging.error(traceback.format_exc())
                    print(f"× {error_msg}")
                    error_count += 1

                    # 从待处理列表中移除出错的任务
                    future_to_product.pop(future, None)

                    # 创建一个默认的分析结果（默认为中风险）
                    default_data = create_default_product_analysis(
                        product, f"分析错误: {str(e)}"
                    )
                    all_product_analysis_data.append(default_data)
                    medium_high_risk_products.append(
                        default_data
                    )  # 默认为中风险，所以添加到结果列表

    # 检查是否所有产品都已处理
    if completed_count + error_count < total:
        logging.warning(
            f"有{total - completed_count - error_count}个产品未被处理，可能是由于程序中断"
        )

        # 为未处理的产品创建默认分析结果
        processed_indices = {index for _, index in future_to_product.values()}
        for i, product in enumerate(detailed_products):
            if i not in processed_indices:
                default_data = create_default_product_analysis(
                    product, "产品未被处理，可能是由于程序中断"
                )
                all_product_analysis_data.append(default_data)
                medium_high_risk_products.append(
                    default_data
                )  # 默认为中风险，所以添加到结果列表
                logging.warning(f"为未处理的产品创建默认分析结果: {product['title']}")

    # 统计中/高风险产品数量
    medium_high_count = len(medium_high_risk_products)

    logging.info(
        f"并行分析完成，成功分析{completed_count}个产品，失败{error_count}个产品"
    )
    logging.info(
        f"风险级别统计: 中/高风险: {medium_high_count}个，低风险: {skipped_count}个"
    )
    print(f"并行分析完成，成功分析{completed_count}个产品，失败{error_count}个产品")
    print(f"风险级别统计: 中/高风险: {medium_high_count}个，低风险: {skipped_count}个")

    # 返回只包含中/高风险的产品列表
    return medium_high_risk_products


def create_default_product_analysis(product, error_message):
    """
    为出错的产品创建默认分析结果

    参数:
        product (dict): 产品信息
        error_message (str): 错误信息

    返回:
        dict: 默认的产品分析结果
    """
    return {
        "id": product["id"],
        "title": product["title"],
        "handle": product["handle"],
        "product_type": product.get("product_type", ""),
        "vendor": product.get("vendor", ""),
        "risk_level": "中",  # 默认中风险
        "issues_count": 1,
        "issues": [
            {
                "type": "分析错误",
                "severity": "中",
                "description": error_message,
                "policy": "无法应用政策",
                "suggestion": "请手动检查此产品",
            }
        ],
        "fix_suggestion": f"无法完成分析: {error_message}，请手动检查此产品",
        # 已移除图片URL
        "link": (
            f"https://{CUSTOM_DOMAIN}/products/{product['handle']}"
            if CUSTOM_DOMAIN
            else f"https://{SHOP_NAME}.myshopify.com/products/{product['handle']}"
        ),
    }


# ===== 产品分析相关函数 =====
def get_detailed_product_info(products):
    """获取更全面的产品信息用于政策分析"""
    logging.info(f"开始获取详细产品信息，共有{len(products)}个产品")
    detailed_products = []
    h = html2text.HTML2Text()
    h.ignore_links = False
    h.ignore_images = True  # 忽略图片，不需要分析图片

    for i, product in enumerate(products):
        # if product.get('status') != 'active':
        #     logging.debug(f"跳过非活跃产品: {product.get('title', '未知标题')} (ID: {product.get('id', '未知ID')})")
        #     continue

        logging.debug(
            f"处理产品 {i+1}/{len(products)}: {product.get('title', '未知标题')}"
        )

        # 创建HTML到文本转换器解析产品描述
        html_description = product.get("body_html", "")
        plain_description = h.handle(html_description) if html_description else ""

        # 收集所有变体信息
        variants_info = []
        price_range = {"min": None, "max": None}
        inventory_total = 0

        for variant in product.get("variants", []):
            price = float(variant.get("price", 0))
            inventory = int(variant.get("inventory_quantity", 0))
            inventory_total += inventory

            # 更新价格范围
            if price_range["min"] is None or price < price_range["min"]:
                price_range["min"] = price
            if price_range["max"] is None or price > price_range["max"]:
                price_range["max"] = price

            variant_info = {
                "title": variant.get("title"),
                "price": price,
                "sku": variant.get("sku", ""),
                "inventory": inventory,
                "barcode": variant.get("barcode", ""),
            }
            variants_info.append(variant_info)

        # 组合详细产品信息
        product_info = {
            "id": product["id"],
            "title": product["title"],
            "handle": product["handle"],
            "description": plain_description,
            "product_type": product.get("product_type", ""),
            "vendor": product.get("vendor", ""),
            "tags": product.get("tags", ""),
            "price_range": price_range,
            "total_inventory": inventory_total,
            "variants": variants_info,
            "published": product.get("published_at") is not None,
            "created_at": product.get("created_at", ""),
            "updated_at": product.get("updated_at", ""),
        }

        # 特定类别的产品检测功能已移除
        product_info["special_categories"] = []

        detailed_products.append(product_info)

    logging.info(f"详细产品信息获取完成，共处理{len(detailed_products)}个产品")
    return detailed_products


def query_policy_db_for_product(product, reset_db=False):
    """
    为特定产品查询相关政策，使用更精确的查询策略

    参数:
        product (dict): 产品信息
        reset_db (bool): 是否重置数据库，如果为True则删除现有集合并重新创建

    返回:
        dict: 查询结果，包含documents、metadatas和distances
    """
    logging.info(f"为产品'{product['title']}'查询相关政策")

    # 默认政策信息
    default_policies = {
        "documents": [
            [
                "Google Merchant Center requires merchants to provide accurate and complete product information, including clear product titles and descriptions.",
                "Merchants must comply with Google Merchant Center policies, including prohibitions on selling restricted or prohibited products.",
                "Merchants must provide clear refund policies, privacy policies, and terms of service.",
            ]
        ],
        "metadatas": [
            [
                {"标题1": "Google Merchant Center", "标题2": "Basic Requirements"},
                {"标题1": "Google Merchant Center", "标题2": "Policy Compliance"},
                {"标题1": "Google Merchant Center", "标题2": "Merchant Policies"},
            ]
        ],
        "distances": [[0.1, 0.2, 0.3]],
    }

    try:
        # 尝试初始化ChromaDB，可选择重置数据库
        try:
            collection = initialize_chroma_db(reset=reset_db)
        except Exception as e:
            logging.error(f"初始化ChromaDB失败: {str(e)}")

            # 如果是因为集合已存在的错误，尝试重置数据库
            if "already exists" in str(e) and not reset_db:
                logging.info("尝试重置数据库并重新初始化...")
                print(f"⟳ 尝试重置数据库并重新初始化...")
                try:
                    return query_policy_db_for_product(
                        product, reset_db=True
                    )  # 递归调用，但启用重置
                except Exception as e2:
                    logging.error(f"重置数据库后初始化仍然失败: {str(e2)}")
                    print(f"× 重置数据库后初始化仍然失败: {str(e2)}")

            # 返回默认政策信息
            logging.warning("ChromaDB初始化失败，返回默认政策信息")
            print(f"! ChromaDB初始化失败，返回默认政策信息")
            return default_policies

        # 提取产品关键信息，用于构建更精确的查询
        product_type = product.get("product_type", "").strip()
        # 这些变量虽然未使用，但保留以便将来扩展

        # 定义空结果，用于错误处理
        empty_result = {"documents": [[]], "metadatas": [[]], "distances": [[]]}
        # vendor = product.get('vendor', '').strip()
        # tags = product.get('tags', '').strip()
        special_categories = product.get("special_categories", [])

        # 构建更结构化的查询
        structured_queries = []

        # 1. 基于产品类型的查询
        if product_type:
            structured_queries.append(
                f"Google Merchant Center {product_type} product policy requirements"
            )

        # 基于特殊类别的精确查询功能已移除

        # 3. 基于产品标题的查询
        title_keywords = extract_keywords(product["title"], 5)
        if title_keywords:
            structured_queries.append(
                f"Google Merchant Center {' '.join(title_keywords)} policy"
            )

        # 4. 基于产品描述的查询
        # 提取描述中的关键句子，而不仅仅是前200个字符
        key_sentences = extract_key_sentences(product["description"], 3)
        for sentence in key_sentences:
            if len(sentence) > 20:  # 忽略太短的句子
                structured_queries.append(sentence)

        # 5. 添加通用政策查询
        structured_queries.append(
            "Google Merchant Center product data quality requirements"
        )
        structured_queries.append("Google Merchant Center product description policy")
        structured_queries.append("Google Merchant Center prohibited content policy")

        logging.debug(f"构建了{len(structured_queries)}个结构化查询")

        # 汇总结果
        all_results = {"documents": [], "metadatas": [], "distances": []}

        # 对每个查询获取结果，并根据相关性排序
        for query in structured_queries:
            if not query.strip():
                continue

            try:
                logging.info(f"执行查询: {query}")
                result = collection.query(
                    query_texts=[query],
                    n_results=5,  # 增加每个查询的结果数量
                    include=["documents", "metadatas", "distances"],
                )

                if (
                    result["documents"]
                    and len(result["documents"]) > 0
                    and len(result["documents"][0]) > 0
                ):
                    logging.info(
                        f"查询'{query}'返回了{len(result['documents'][0])}个结果"
                    )

                    # 记录每个结果的详细信息（仅用于调试）
                    if logging.getLogger().level <= logging.DEBUG:
                        for i, doc in enumerate(result["documents"][0]):
                            doc_preview = doc[:50] + "..." if len(doc) > 50 else doc
                            distance = result["distances"][0][i]
                            similarity = 1 - distance  # 转换为相似度分数
                            metadata = result["metadatas"][0][i]
                            logging.debug(
                                f"结果 {i+1}: {doc_preview}, 相似度: {similarity:.4f}, 元数据: {metadata}"
                            )

                    all_results["documents"].extend(result["documents"][0])
                    all_results["metadatas"].extend(result["metadatas"][0])
                    all_results["distances"].extend(result["distances"][0])
                else:
                    logging.warning(f"查询'{query}'未返回任何结果")
            except Exception as e:
                logging.warning(f"执行查询'{query}'失败: {str(e)}")
                continue

        # 如果没有获取到任何结果，返回默认结果
        if not all_results["documents"]:
            logging.warning("未获取到任何查询结果，返回默认政策信息")
            return {
                "documents": [
                    [
                        "谷歌购物广告要求商家提供准确、完整的产品信息，包括清晰的产品标题、描述。",
                        "商家必须遵守谷歌购物广告的政策，包括禁止销售受限制或禁止的产品。",
                        "商家必须提供明确的退款政策、隐私政策和服务条款。",
                    ]
                ],
                "metadatas": [
                    [
                        {"标题1": "谷歌购物广告", "标题2": "基本要求"},
                        {"标题1": "谷歌购物广告", "标题2": "政策合规"},
                        {"标题1": "谷歌购物广告", "标题2": "商家政策"},
                    ]
                ],
                "distances": [[0.1, 0.2, 0.3]],
            }

        # 去重结果并按相关性排序
        try:
            unique_items = []
            seen_docs = set()

            for i, doc in enumerate(all_results["documents"]):
                # 使用文档前100个字符作为唯一性检查
                doc_key = doc[:100]
                if doc_key not in seen_docs:
                    seen_docs.add(doc_key)
                    unique_items.append(
                        {
                            "document": doc,
                            "metadata": all_results["metadatas"][i],
                            "distance": all_results["distances"][i],
                        }
                    )

            # 按相关性排序（距离越小越相关）
            unique_items.sort(key=lambda x: x["distance"])

            # 提取排序后的结果
            unique_docs = [item["document"] for item in unique_items]
            unique_metas = [item["metadata"] for item in unique_items]
            unique_distances = [item["distance"] for item in unique_items]

            # 限制最终结果数量，但确保至少包含一些通用政策
            max_results = min(12, len(unique_docs))  # 增加结果数量以提供更全面的上下文
            logging.info(f"找到{len(unique_docs)}个相关政策，使用前{max_results}个")

            return {
                "documents": [unique_docs[:max_results]],
                "metadatas": [unique_metas[:max_results]],
                "distances": [unique_distances[:max_results]],
            }
        except Exception as e:
            logging.error(f"处理查询结果时出错: {str(e)}")
            return empty_result

    except Exception as e:
        logging.error(f"查询政策数据库失败: {str(e)}")
        traceback.print_exc()
        return empty_result


def extract_keywords(text, max_keywords=5):
    """从文本中提取关键词"""
    if not text:
        return []

    # 简单的关键词提取逻辑，可以根据需要使用更复杂的算法
    # 移除常见停用词
    stopwords = {
        "的",
        "了",
        "和",
        "是",
        "在",
        "有",
        "与",
        "这",
        "那",
        "为",
        "及",
        "或",
        "等",
        "the",
        "a",
        "an",
        "and",
        "or",
        "for",
        "to",
        "in",
        "on",
        "with",
    }

    # 分词并过滤
    words = re.findall(r"\w+", text.lower())
    keywords = [word for word in words if word not in stopwords and len(word) > 1]

    # 计算词频
    word_freq = {}
    for word in keywords:
        word_freq[word] = word_freq.get(word, 0) + 1

    # 按频率排序并返回前N个
    sorted_keywords = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
    return [word for word, _ in sorted_keywords[:max_keywords]]


def extract_key_sentences(text, max_sentences=3):
    """从文本中提取关键句子"""
    if not text:
        return []

    # 分割成句子
    sentences = re.split(r"[.!?。！？\n]+", text)
    sentences = [s.strip() for s in sentences if s.strip()]

    if not sentences:
        return []

    # 如果句子数量少于要求的最大数量，直接返回所有句子
    if len(sentences) <= max_sentences:
        return sentences

    # 简单策略：返回第一句、最长的句子和最后一句
    first_sentence = sentences[0]
    last_sentence = sentences[-1]

    # 找出最长的句子（不包括第一句和最后一句）
    middle_sentences = sentences[1:-1]
    if middle_sentences:
        longest_sentence = max(middle_sentences, key=len)
        return [first_sentence, longest_sentence, last_sentence]
    else:
        return [first_sentence, last_sentence]


def analyze_product_with_deepseek(product, relevant_policies):
    """使用AI模型分析产品是否符合政策，提供更详细的分析和具体建议"""
    active_model = CONFIG.get("active_model", "deepseek")
    model_name = CONFIG["models"][active_model].get("name", "AI模型")
    logging.info(f"使用{model_name}分析产品: {product['title']}")

    # 准备产品信息
    price_info = ""
    if product["price_range"]["min"] is not None:
        # 格式化价格，整数显示为整数，小数显示为小数
        def format_price(price):
            # 检查价格是否为整数
            if price == int(price):
                return f"${int(price)}"  # 如果是整数，去掉小数部分
            else:
                return f"${price}"  # 如果有小数，保留小数

        min_price = format_price(product["price_range"]["min"])
        max_price = format_price(product["price_range"]["max"])

        if product["price_range"]["min"] == product["price_range"]["max"]:
            price_info = f"价格: {min_price}"
        else:
            price_info = f"价格范围: {min_price} - {max_price}"

    variants_info = ""
    for i, variant in enumerate(product["variants"]):  # 显示全部变体
        # 格式化变体价格
        variant_price = variant["price"]
        if variant_price == int(variant_price):
            formatted_price = f"${int(variant_price)}"
        else:
            formatted_price = f"${variant_price}"

        variants_info += f"变体{i+1}: {variant['title']}, 价格: {formatted_price}, SKU: {variant['sku']}, 库存: {variant['inventory']}\n"

    # 构建更结构化的产品信息
    product_info = f"""
    ## 基本信息
    产品ID: {product['id']}
    产品标题: {product['title']}
    品牌/供应商: {product['vendor']}
    产品类型: {product['product_type']}
    标签: {product['tags']}
    {price_info}
    总库存: {product['total_inventory']}

    ## 产品描述
    {product['description']}

    ## 变体信息
    {variants_info}

    ## 产品链接
    https://{SHOP_NAME}.myshopify.com/products/{product['handle']}
    """

    # 准备相关政策信息，按相关性排序并提供更完整的内容
    policy_info = ""
    policy_titles = []

    # 首先收集所有政策标题，以便后续分析
    for i, doc in enumerate(relevant_policies["documents"][0]):
        metadata = relevant_policies["metadatas"][0][i]
        title_path = []
        for j in range(1, 5):
            key = f"标题{j}"
            if key in metadata:
                title_path.append(metadata[key])
        title = " > ".join(title_path) if title_path else "未知标题"
        policy_titles.append(title)

    # 添加完整的政策内容，不再截断
    for i, doc in enumerate(relevant_policies["documents"][0]):
        # 使用完整的政策内容，不进行截断
        doc_content = doc
        policy_info += f"### 政策{i+1}：{policy_titles[i]}\n{doc_content}\n\n"

    logging.debug(f"准备了{len(relevant_policies['documents'][0])}条相关政策信息")

    # 使用更结构化的JSON模板，包含更多分析维度
    json_template = """
    {
      "analysis": {
        "overall_risk": "低/中/高",
        "issues": [
          {
            "issue_type": "产品标题/描述/价格/政策/其他",
            "severity": "低/中/高",
            "description": "具体问题描述",
            "policy_reference": "相关政策编号或名称",
            "fix_suggestion": "具体修复建议"
          }
        ]
      },
      "summary": {
        "gmc_risk": "低/中/高",
        "fix_suggestion": "总体修复建议"
      }
    }
    """

    # 更详细的提示词，引导模型进行更深入的分析
    prompt = f"""
    # 谷歌购物广告(GMC)政策合规分析

    ## 任务背景
    您是谷歌购物广告政策合规分析专家。您需要分析以下Shopify商店产品是否符合谷歌购物广告政策要求，并提供详细的合规性分析和具体的修复建议，修复建议需要有示例文本。

    ## 产品信息
    {product_info}

    ## 相关GMC政策
    {policy_info}

    ## 分析要求
    请对产品进行全面分析，检查以下方面是否符合GMC政策要求：

    1. **产品标题**：是否清晰、准确，不含有禁止内容或误导性表述
    2. **产品描述**：是否详细、准确，不含有禁止内容、虚假声明或误导性表述
    3. **产品类型和类别**：是否属于受限或禁止类别
    4. **价格信息**：是否清晰、合理
    5. **特殊政策要求**：根据产品类型，是否符合特定类别的政策要求

    ##注意：
    1. 该商店网站语言为英文，面向英语市场
    2. 该商店使用了自定义域名(如sflamps.com)，请不要将使用自定义域名(如sflamps.com)标记为问题。这是商店的正式域名，不是临时域名。请使用正式域名进行分析。

    ## 修复建议要求
    对于每个发现的问题，请提供具体的修复建议，包括：

    1. **明确的修改内容**：不要只说"修改描述中的艺术化语言"，而应该明确指出原文是什么，应该改成什么。例如："将'put you in the crystal cave and interstellar space'改为'provide elegant lighting effects'或'add a refined ambiance to your space'"。

    2. **具体的操作步骤**：如果需要添加内容，请提供具体的示例文本；如果需要删除内容，请明确指出需要删除的具体内容。

    3. **替代方案**：如果有多种可能的修复方式，请提供2-3种不同的替代方案，让用户可以选择最适合的一种。

    4. **极其重要**：每个问题的fix_suggestion字段必须包含具体的修改建议，而不是笼统的描述。例如，不要写"描述中使用了艺术化语言"，而应该写"将'put you in the crystal cave and interstellar space'改为'creates a beautiful lighting effect'或'enhances your room's ambiance'"。

    ## 输出格式要求
    请以JSON格式输出您的分析结果，包含以下内容：

    1. 详细分析：列出所有发现的问题，包括问题类型、严重程度、问题描述、相关政策参考和修复建议
    2. 总体风险评估：低、中、高三个级别之一
    3. 总体修复建议：简明扼要的具体优化修复建议

    你必须严格按照以下JSON格式输出，不要有任何其他文字说明。确保JSON格式完全正确，没有任何语法错误：
    - 使用双引号而不是单引号
    - 确保所有属性名都有双引号
    - 确保所有字符串值都有双引号
    - 确保对象和数组中的元素之间有逗号分隔
    - 确保最后一个元素后没有逗号
    - 布尔值使用true/false而不是True/False
    - null值使用null而不是None
    ```json
{json_template}
    ```

    ## 分析指南
    - 如果没有发现任何问题，issues数组可以为空，overall_risk应为"低"
    - 严重问题（可能导致拒绝投放）应标记为"高"风险
    - 轻微问题（可能影响性能但不会拒绝投放）应标记为"低"风险
    - 每个问题都应提供具体、可操作的修复建议
    - 修复建议应具体明确，例如"将'神奇减肥药'改为'健康饮食辅助产品'"，而不是笼统的"修改产品描述"

    ## 重要提示
    请确保你的输出是有效的JSON格式，可以被JSON解析器直接解析。不要添加任何额外的文本、解释或注释。
    """

    # 记录请求耗时
    start_time = time.time()
    logging.debug("开始调用AI模型...")

    try:
        # 调用AI模型API进行分析
        active_model = CONFIG.get("active_model", "deepseek")
        model_name = CONFIG["models"][active_model].get("name", "AI模型")

        # 使用配置文件中的参数
        response = call_ai_model_api(prompt)
        elapsed_time = time.time() - start_time
        logging.info(f"{model_name}分析完成，耗时: {elapsed_time:.2f}秒")

        return response
    except Exception as e:
        elapsed_time = time.time() - start_time
        logging.error(f"AI模型分析失败，耗时: {elapsed_time:.2f}秒，错误: {str(e)}")
        traceback.print_exc()
        return f"分析出错: {str(e)}"


def extract_product_analysis_data(product, analysis_text, max_retries=3, retry_delay=5):
    """
    从分析文本中提取产品评分和风险数据，支持新的结构化JSON格式

    参数:
        product (dict): 产品信息
        analysis_text (str): AI分析文本
        max_retries (int): 最大重试次数
        retry_delay (int): 初始重试间隔时间（秒）

    返回:
        dict: 处理后的产品数据
    """
    logging.info(f"提取产品'{product['title']}'的分析数据")

    # 初始化默认值
    risk_level = "中"  # 解析失败时默认为中风险
    fix_suggestion = "无具体建议"
    issues = []

    # 检查分析文本是否为空或无效
    if (
        not analysis_text
        or analysis_text.startswith("分析出错:")
        or analysis_text.startswith("API调用失败")
    ):
        logging.error(f"分析文本无效: {analysis_text[:100]}...")

        # 检查是否包含速率限制相关错误
        if (
            "rate limit" in analysis_text.lower()
            or "请求频率" in analysis_text
            or "429" in analysis_text
        ):
            logging.warning("检测到速率限制错误，将重新分析产品")
            # 重新分析产品
            for attempt in range(max_retries):
                logging.info(f"重试分析产品 (尝试 {attempt+1}/{max_retries})")
                print(
                    f"⟳ 重试分析产品 '{product['title']}' (尝试 {attempt+1}/{max_retries})"
                )

                # 等待一段时间后重试，使用指数退避策略
                wait_time = retry_delay * (2**attempt)
                logging.info(f"等待{wait_time}秒后重试...")
                time.sleep(wait_time)

                # 重新查询相关政策
                relevant_policies = query_policy_db_for_product(product)

                # 重新使用AI模型分析
                new_analysis = analyze_product_with_deepseek(product, relevant_policies)

                # 如果新分析结果有效，则递归调用自身处理新结果（但不再重试，避免无限递归）
                if (
                    new_analysis
                    and not new_analysis.startswith("分析出错:")
                    and not new_analysis.startswith("API调用失败")
                ):
                    logging.info("重试成功获取有效分析结果")
                    return extract_product_analysis_data(
                        product, new_analysis, max_retries=0
                    )

            # 如果所有重试都失败
            logging.error(f"所有重试都失败，使用默认结果")
            fix_suggestion = (
                f"AI分析失败（已重试{max_retries}次）: {analysis_text[:100]}"
            )
        else:
            fix_suggestion = f"AI分析失败: {analysis_text[:100]}"

        return prepare_product_data(product, risk_level, fix_suggestion, issues)

    # 尝试解析JSON响应，添加重试机制
    for attempt in range(max_retries + 1):  # +1 是为了包括第一次尝试
        try:
            # 首先尝试查找JSON代码块
            json_match = re.search(r"```json\s*(.*?)\s*```", analysis_text, re.DOTALL)
            if json_match:
                try:
                    json_str = json_match.group(1).strip()
                    # 检查JSON字符串是否有效
                    if json_str and not json_str.isspace():
                        analysis_json = json.loads(json_str)
                        logging.debug("从代码块中成功解析JSON")
                    else:
                        logging.warning("JSON代码块为空")
                        raise ValueError("JSON代码块为空")
                except json.JSONDecodeError as je:
                    logging.warning(f"JSON代码块解析失败: {str(je)}")
                    # 尝试清理JSON字符串中的问题
                    json_str = clean_json_string(json_match.group(1))
                    analysis_json = json.loads(json_str)
                    logging.debug("清理后从代码块中成功解析JSON")
            else:
                # 尝试在文本中查找JSON对象
                json_obj_match = re.search(r"(\{.*\})", analysis_text, re.DOTALL)
                if json_obj_match:
                    try:
                        json_str = json_obj_match.group(1).strip()
                        analysis_json = json.loads(json_str)
                        logging.debug("从文本中提取JSON对象成功")
                    except json.JSONDecodeError:
                        # 尝试清理JSON字符串
                        json_str = clean_json_string(json_obj_match.group(1))
                        analysis_json = json.loads(json_str)
                        logging.debug("清理后从文本中提取JSON对象成功")
                else:
                    # 最后尝试直接解析整个文本
                    try:
                        analysis_json = json.loads(analysis_text)
                        logging.debug("直接解析文本为JSON成功")
                    except json.JSONDecodeError:
                        # 尝试清理整个文本
                        json_str = clean_json_string(analysis_text)
                        analysis_json = json.loads(json_str)
                        logging.debug("清理后直接解析文本为JSON成功")

            # 从JSON结构中提取风险和建议
            if "summary" in analysis_json:
                # 新格式
                risk_level = analysis_json["summary"].get("gmc_risk", "中")
                fix_suggestion = analysis_json["summary"].get(
                    "fix_suggestion", "无具体建议"
                )

                # 提取详细问题列表
                if (
                    "analysis" in analysis_json
                    and "issues" in analysis_json["analysis"]
                ):
                    for issue in analysis_json["analysis"]["issues"]:
                        issues.append(
                            {
                                "type": issue.get("issue_type", "未知"),
                                "severity": issue.get("severity", "中"),
                                "description": issue.get("description", ""),
                                "policy": issue.get("policy_reference", ""),
                                "suggestion": issue.get("fix_suggestion", ""),
                            }
                        )

                logging.info(
                    f"提取的风险级别: {risk_level}, 修复建议长度: {len(fix_suggestion)}, 问题数量: {len(issues)}"
                )
            else:
                # 旧格式兼容
                risk_level = analysis_json.get("gmc_risk", "中")
                fix_suggestion = analysis_json.get("fix_suggestion", "无具体建议")
                logging.info(
                    f"使用旧格式解析，提取的风险级别: {risk_level}, 修复建议长度: {len(fix_suggestion)}"
                )

            # 成功解析，跳出重试循环
            break

        except Exception as e:
            if attempt < max_retries:
                # 如果不是最后一次尝试，则等待后重试
                wait_time = retry_delay * (2**attempt)  # 指数退避策略
                logging.warning(
                    f"解析JSON失败 (尝试 {attempt+1}/{max_retries+1}): {str(e)}，等待{wait_time}秒后重试..."
                )
                print(
                    f"⟳ 解析产品'{product['title']}'的AI响应失败，等待{wait_time}秒后重试..."
                )
                time.sleep(wait_time)
            else:
                # 最后一次尝试也失败，尝试让AI修复JSON格式
                logging.error(f"所有解析尝试都失败: {str(e)}")
                logging.debug(f"原始响应文本: {analysis_text[:200]}...")

                # 尝试让AI修复JSON格式
                logging.info("尝试让AI修复JSON格式...")
                print(f"⟳ 尝试让AI修复JSON格式...")

                try:
                    # 调用AI修复JSON格式
                    fixed_json = fix_json_with_ai(analysis_text, product["title"])
                    if fixed_json:
                        # 尝试解析修复后的JSON
                        analysis_json = json.loads(fixed_json)
                        logging.info("AI成功修复了JSON格式")

                        # 从修复后的JSON中提取数据
                        if "summary" in analysis_json:
                            risk_level = analysis_json["summary"].get("gmc_risk", "中")
                            fix_suggestion = analysis_json["summary"].get(
                                "fix_suggestion", "无具体建议"
                            )

                            # 提取详细问题列表
                            if (
                                "analysis" in analysis_json
                                and "issues" in analysis_json["analysis"]
                            ):
                                for issue in analysis_json["analysis"]["issues"]:
                                    issues.append(
                                        {
                                            "type": issue.get("issue_type", "未知"),
                                            "severity": issue.get("severity", "中"),
                                            "description": issue.get("description", ""),
                                            "policy": issue.get("policy_reference", ""),
                                            "suggestion": issue.get(
                                                "fix_suggestion", ""
                                            ),
                                        }
                                    )

                            logging.info(
                                f"从修复的JSON中提取的风险级别: {risk_level}, 修复建议长度: {len(fix_suggestion)}, 问题数量: {len(issues)}"
                            )
                            break
                        else:
                            # 旧格式兼容
                            risk_level = analysis_json.get("gmc_risk", "中")
                            fix_suggestion = analysis_json.get(
                                "fix_suggestion", "无具体建议"
                            )
                            logging.info(
                                f"从修复的JSON中使用旧格式解析，提取的风险级别: {risk_level}, 修复建议长度: {len(fix_suggestion)}"
                            )
                            break
                    else:
                        logging.warning("AI未能修复JSON格式")
                        fix_suggestion = "无法解析AI响应，请手动检查"
                except Exception as fix_error:
                    logging.error(f"尝试修复JSON格式时出错: {str(fix_error)}")
                    fix_suggestion = "无法解析AI响应，请手动检查"
                    traceback.print_exc()

    return prepare_product_data(product, risk_level, fix_suggestion, issues)


def fix_json_with_ai(analysis_text, product_title):
    """
    使用AI修复格式不正确的JSON

    参数:
        analysis_text (str): 原始AI分析文本
        product_title (str): 产品标题，用于日志记录

    返回:
        str: 修复后的JSON字符串，如果修复失败则返回None
    """
    logging.info(f"开始使用AI修复产品'{product_title}'的JSON格式")

    # 构建提示词
    prompt = f"""
    # JSON格式修复任务

    ## 任务背景
    我收到了一个AI生成的JSON响应，但格式有问题，无法被解析。请帮我修复这个JSON，使其符合标准格式。

    ## 原始JSON文本
    ```
    {analysis_text}
    ```

    ## 要求
    1. 仅输出修复后的JSON，不要有任何其他解释或评论
    2. 确保所有属性名和字符串值都使用双引号
    3. 确保对象和数组中的元素之间有逗号分隔
    4. 确保最后一个元素后没有逗号
    5. 确保布尔值使用true/false而不是True/False
    6. 确保null值使用null而不是None
    7. 保持原始JSON的结构和内容不变，只修复格式问题

    ## 输出格式
    只输出修复后的JSON，不要有任何其他文本。不要使用代码块标记。
    """

    try:
        # 调用AI模型API进行修复
        active_model = CONFIG.get("active_model", "deepseek")
        model_name = CONFIG["models"][active_model].get("name", "AI模型")

        logging.info(f"使用{model_name}修复JSON格式")
        fixed_json = call_ai_model_api(prompt)

        if fixed_json:
            # 清理可能的代码块标记
            fixed_json = re.sub(r"^```(json)?\s*", "", fixed_json)
            fixed_json = re.sub(r"\s*```$", "", fixed_json)

            # 验证修复后的JSON是否有效
            try:
                json.loads(fixed_json)
                logging.info("JSON格式修复成功")
                return fixed_json
            except json.JSONDecodeError as je:
                logging.warning(f"AI修复后的JSON仍然无效: {str(je)}")
                # 尝试使用clean_json_string进一步清理
                cleaned_json = clean_json_string(fixed_json)
                try:
                    json.loads(cleaned_json)
                    logging.info("JSON格式在进一步清理后修复成功")
                    return cleaned_json
                except:
                    logging.error("无法修复JSON格式")
                    return None
        else:
            logging.warning("AI未返回有效的修复结果")
            return None
    except Exception as e:
        logging.error(f"使用AI修复JSON格式时出错: {str(e)}")
        return None


def clean_json_string(json_str):
    """清理JSON字符串中的常见问题"""
    # 记录原始字符串长度，用于调试
    original_length = len(json_str)

    # 移除前导和尾随的非JSON字符
    json_str = re.sub(r"^[^{\[]+", "", json_str)
    json_str = re.sub(r"[^}\]]+$", "", json_str)

    # 修复常见的JSON格式问题
    # 1. 将单引号替换为双引号（处理属性名和字符串值）
    json_str = re.sub(r"'([^']*)':", r'"\1":', json_str)
    json_str = re.sub(r": *\'([^\']*)\'", r': "\1"', json_str)

    # 2. 修复尾随逗号
    json_str = re.sub(r",\s*([}\]])", r"\1", json_str)

    # 3. 确保属性名有双引号
    json_str = re.sub(r"([{,])\s*([a-zA-Z0-9_]+)\s*:", r'\1"\2":', json_str)

    # 4. 修复缺少逗号的问题（在对象内的两个属性之间）
    # 查找模式：属性值后面直接跟着另一个属性名，中间没有逗号
    json_str = re.sub(r'("[^"]*")\s*\n?\s*("?[a-zA-Z0-9_]+"?\s*:)', r"\1,\2", json_str)

    # 5. 修复缺少逗号的问题（在数组内的两个元素之间）
    json_str = re.sub(r'(["}\]]\s*)\n?\s*({|\[|")', r"\1,\2", json_str)

    # 6. 修复多余的逗号（连续的逗号）
    json_str = re.sub(r",\s*,", ",", json_str)

    # 7. 修复错误的布尔值和null值格式
    json_str = re.sub(r":\s*True\b", r": true", json_str)
    json_str = re.sub(r":\s*False\b", r": false", json_str)
    json_str = re.sub(r":\s*None\b", r": null", json_str)

    # 8. 修复未闭合的引号
    # 查找模式：引号开始但没有结束的字符串
    lines = json_str.split("\n")
    for i in range(len(lines)):
        # 计算行中的引号数量
        quote_count = lines[i].count('"')
        if quote_count % 2 == 1:  # 奇数个引号表示有未闭合的引号
            # 在行尾添加引号
            lines[i] = lines[i] + '"'
    json_str = "\n".join(lines)

    # 9. 修复未闭合的对象或数组
    # 计算左右括号的数量
    open_braces = json_str.count("{")
    close_braces = json_str.count("}")
    open_brackets = json_str.count("[")
    close_brackets = json_str.count("]")

    # 添加缺少的右括号
    if open_braces > close_braces:
        json_str += "}" * (open_braces - close_braces)

    # 添加缺少的右方括号
    if open_brackets > close_brackets:
        json_str += "]" * (open_brackets - close_brackets)

    # 10. 尝试修复特定的Gemini 2.5 Flash错误模式
    # 修复行39附近的逗号问题（根据错误信息）
    lines = json_str.split("\n")
    if len(lines) >= 39:
        # 检查第39行是否有可能的格式问题
        line39 = lines[38]  # 索引从0开始，所以第39行是索引38
        # 查找可能缺少逗号的模式
        if re.search(r'"\s*}', line39) or re.search(r'"\s*]', line39):
            # 在引号和右括号之间添加逗号
            lines[38] = re.sub(r'(")\s*(}|])', r"\1,\2", line39)
            json_str = "\n".join(lines)

    # 记录清理后的字符串长度，用于调试
    cleaned_length = len(json_str)
    logging.debug(
        f"JSON字符串清理: 原始长度={original_length}, 清理后长度={cleaned_length}"
    )

    return json_str


def prepare_product_data(product, risk_level, fix_suggestion, issues):
    """准备产品数据对象"""

    # 构建产品数据
    price_info = ""
    if product["price_range"]["min"] is not None:
        # 格式化价格，整数显示为整数，小数显示为小数
        def format_price(price):
            # 检查价格是否为整数
            if price == int(price):
                return f"${int(price)}"  # 如果是整数，去掉小数部分
            else:
                return f"${price}"  # 如果有小数，保留小数

        min_price = format_price(product["price_range"]["min"])
        max_price = format_price(product["price_range"]["max"])

        if product["price_range"]["min"] == product["price_range"]["max"]:
            price_info = min_price
        else:
            price_info = f"{min_price} - {max_price}"

    # 处理描述，保留更多有用信息
    description = product["description"]
    if len(description) > 150:
        # 提取前150个字符，但尝试在句子结束处截断
        cutoff = min(150, len(description))
        last_period = description[:cutoff].rfind("。")
        last_exclamation = description[:cutoff].rfind("！")
        last_question = description[:cutoff].rfind("？")
        last_sentence_end = max(last_period, last_exclamation, last_question)

        if last_sentence_end > 50:  # 确保至少有一定长度
            description = description[: last_sentence_end + 1] + "..."
        else:
            description = description[:cutoff] + "..."

    # 构建产品链接，优先使用自定义域名
    if CUSTOM_DOMAIN:
        product_link = f"https://{CUSTOM_DOMAIN}/products/{product['handle']}"
    else:
        product_link = f"https://{SHOP_NAME}.myshopify.com/products/{product['handle']}"

    # 构建更丰富的结果对象
    result = {
        "product_id": product["id"],
        "title": product["title"],
        "description": description,
        "link": product_link,
        "price": price_info,
        "inventory": product["total_inventory"],
        "product_type": product.get("product_type", ""),
        "gmc_risk": risk_level,
        "fix_suggestion": fix_suggestion,
        "issues": issues,
        "issues_count": len(issues),
    }

    # 为飞书表格准备完整版本的问题列表，包含问题描述和修复建议
    if issues:
        issues_summary = []
        for i, issue in enumerate(issues):  # 显示所有问题，不限制数量
            # 合并问题描述和修复建议
            issue_text = (
                f"{i+1}. [{issue['severity']}] {issue['type']}: {issue['description']}"
            )
            if issue.get("suggestion"):
                issue_text += f"\n   修复建议: {issue['suggestion']}"
            issues_summary.append(issue_text)

        result["issues_summary"] = "\n".join(issues_summary)
        # 添加问题及修复字段，用于新的表格列
        result["issues_and_fixes"] = result["issues_summary"]
    else:
        result["issues_summary"] = "无具体问题"
        result["issues_and_fixes"] = "无具体问题"

    logging.debug(
        f"产品数据提取完成: {result['title']}, 风险级别: {result['gmc_risk']}, 问题数量: {result['issues_count']}"
    )
    return result


# 移除政策分析相关函数

# 移除政策分析相关函数

# 移除政策分析相关函数

# 移除政策分析相关函数

# 移除政策分析相关函数

# 移除政策分析相关函数

# 移除政策分析相关函数


# ===== 飞书API相关函数 =====
def get_feishu_access_token():
    """获取飞书访问令牌"""
    logging.info("开始获取飞书访问令牌")
    url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
    headers = {"Content-Type": "application/json"}
    data = {"app_id": FEISHU_APP_ID, "app_secret": FEISHU_APP_SECRET}

    try:
        response = requests.post(
            url, headers=headers, data=json.dumps(data), timeout=30
        )
        response_json = response.json()

        if response.status_code == 200 and response_json.get("tenant_access_token"):
            logging.info("成功获取飞书访问令牌")
            return response_json.get("tenant_access_token")
        else:
            error_msg = f"获取飞书访问令牌失败: HTTP {response.status_code}, 响应: {response_json}"
            logging.error(error_msg)
            return None
    except Exception as e:
        error_msg = f"获取飞书访问令牌异常: {str(e)}"
        logging.error(error_msg)
        return None


# 导入增强版飞书表格写入函数
from write_to_feishu_sheet_enhanced import write_to_feishu_sheet


def write_to_feishu_sheet_legacy(sheet_id, data, start_row=2):
    """旧版写入数据到飞书表格函数，保留以兼容现有代码"""
    logging.info(
        f"使用旧版函数写入数据到飞书表格, 工作表ID: {sheet_id}, 数据条数: {len(data)}"
    )
    access_token = get_feishu_access_token()

    if not access_token:
        error_msg = "无法获取飞书访问令牌，无法写入数据"
        logging.error(error_msg)
        print(f"× {error_msg}")
        return False

    # 首先清除表格中的旧数据
    try:
        # 获取表格的行数（首先尝试获取表格元数据）
        metadata_url = f"https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/{SPREADSHEET_TOKEN}/metainfo"
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }

        metadata_response = requests.get(metadata_url, headers=headers, timeout=30)

        if metadata_response.status_code == 200:
            logging.info(f"成功获取表格元数据")

            # 清除表格数据（我们只清除数据区域，保留标题行）
            clear_url = f"https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/{SPREADSHEET_TOKEN}/values_clear"
            clear_data = {
                "range": f"{sheet_id}!A{start_row}:Z1000"  # 清除从开始行到1000行的数据
            }

            clear_response = requests.post(
                clear_url, headers=headers, json=clear_data, timeout=30
            )

            if clear_response.status_code == 200:
                logging.info(f"成功清除表格旧数据")
            else:
                logging.warning(
                    f"清除表格数据失败: {clear_response.status_code}, {clear_response.text}"
                )
        else:
            logging.warning(f"获取表格元数据失败: {metadata_response.status_code}")
    except Exception as e:
        logging.warning(f"清除表格数据异常: {str(e)}")

    # 写入新数据
    url = f"https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/{SPREADSHEET_TOKEN}/values"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {access_token}",
    }

    # 准备要写入的数据
    values = []
    for row in data:
        # 确保每个单元格的值是字符串类型
        row_values = []
        for value in row.values():
            if value is None:
                row_values.append("")
            else:
                # 限制字符串长度，飞书表格单元格可能有字符限制
                value_str = str(value)
                if len(value_str) > 5000:  # 设置一个合理的最大长度
                    value_str = value_str[:4997] + "..."
                row_values.append(value_str)
        values.append(row_values)

    # 构建表格范围字符串
    end_row = start_row + len(values) - 1
    end_col = chr(ord("A") + len(values[0]) - 1) if values else "A"
    range_str = f"{sheet_id}!A{start_row}:{end_col}{end_row}"

    data = {"valueRange": {"range": range_str, "values": values}}

    logging.debug(f"表格范围: {range_str}, 数据行数: {len(values)}")

    try:
        response = requests.put(url, headers=headers, json=data, timeout=60)
        if response.status_code == 200:
            logging.info(f"成功写入 {len(values)} 行数据到表格")
            print(f"成功写入 {len(values)} 行数据到表格")
            return True
        else:
            error_msg = f"写入数据失败: HTTP {response.status_code}, 响应: {response.text[:200]}"
            logging.error(error_msg)
            print(f"写入数据失败: {response.text}")
            return False
    except Exception as e:
        error_msg = f"写入数据异常: {str(e)}"
        logging.error(error_msg)
        print(f"写入数据异常: {e}")
        return False


# ===== 主函数 =====


def main():
    product_analysis_data = []

    try:
        logging.info("=" * 60)
        logging.info("Shopify商品合规性分析及飞书集成工具启动")
        logging.info("=" * 60)

        print("=" * 60)
        print("Shopify商品合规性分析及飞书集成工具")
        print("=" * 60)

        # 检查ChromaDB集合
        print("检查ChromaDB集合状态...")
        try:
            # 先尝试不重置初始化
            collection = initialize_chroma_db(reset=False)

            # 测试查询，检查是否能返回结果
            test_query = "谷歌购物广告 商家政策要求"
            print(f"执行测试查询: {test_query}")
            result = collection.query(
                query_texts=[test_query], n_results=1, include=["documents"]
            )

            if (
                result["documents"]
                and len(result["documents"]) > 0
                and len(result["documents"][0]) > 0
            ):
                print(f"✓ ChromaDB集合正常工作，查询返回了结果")
                logging.info("ChromaDB集合正常工作，查询返回了结果")
            else:
                print(f"! ChromaDB集合存在但查询未返回结果，尝试重置集合...")
                logging.warning("ChromaDB集合存在但查询未返回结果，尝试重置集合")

                # 重置集合
                collection = initialize_chroma_db(reset=True)
                print("✓ ChromaDB集合已重置")

                # 提示用户可能需要运行policy_to_chromadb.py
                print(
                    "! 注意: 集合已重置，如果仍然无法获取查询结果，请运行policy_to_chromadb.py脚本重新向量化政策文档"
                )
                logging.warning(
                    "集合已重置，如果仍然无法获取查询结果，请运行policy_to_chromadb.py脚本重新向量化政策文档"
                )
        except Exception as e:
            print(f"× ChromaDB初始化失败: {str(e)}")
            logging.error(f"ChromaDB初始化失败: {str(e)}")
            print("! 建议运行policy_to_chromadb.py脚本重新向量化政策文档")

        print("\n第一部分：商品合规性分析")
        print("-" * 60)
        logging.info("开始第一部分：商品合规性分析")

        # 添加产品状态选择
        print("\n请选择要分析的产品状态：")
        print("1. 所有产品")
        print("2. 只分析活跃的产品")
        print("3. 只分析已归档的产品")
        print("4. 只分析草稿产品")
        status_choice = input("请输入数字选择产品状态（默认为所有产品）：").strip()

        status_map = {"1": "any", "2": "active", "3": "archived", "4": "draft"}

        product_status = "any"  # 默认为所有产品
        if status_choice in status_map:
            product_status = status_map[status_choice]
            status_display = {
                "any": "所有",
                "active": "活跃的",
                "archived": "已归档的",
                "draft": "草稿",
            }
            print(f"将分析{status_display[product_status]}产品...")
            logging.info(f"选择分析{status_display[product_status]}产品")

        print("正在从Shopify店铺获取产品...")
        logging.info(
            f"从{SHOP_NAME}商店获取{status_display.get(product_status, '所有')}产品信息"
        )
        products = get_all_products(status=product_status)

        if not products:
            error_msg = "未能获取到任何产品。请检查您的API密钥和密码是否正确。"
            logging.error(error_msg)
            print(error_msg)
            return

        logging.info(f"成功获取{len(products)}个产品")
        print(f"总共找到 {len(products)} 个产品")

        # 获取详细产品信息
        detailed_products = get_detailed_product_info(products)
        logging.info(f"处理{len(detailed_products)}个产品")
        print(f"处理 {len(detailed_products)} 个产品")

        # 限制处理产品数量，提高速度
        # max_products = 10  # 最多处理10个产品
        # if len(detailed_products) > max_products:
        #     logging.info(f"限制分析产品数量为{max_products}个，总共有{len(detailed_products)}个")
        #     detailed_products = detailed_products[:max_products]

        # 使用并行处理分析产品
        logging.info(
            f"开始并行分析{len(detailed_products)}个产品，最大工作线程数: {MAX_WORKERS}"
        )
        print(
            f"开始并行分析{len(detailed_products)}个产品，最大工作线程数: {MAX_WORKERS}"
        )
        print(f"注意: 只会显示和发送中/高风险产品，低风险产品将被跳过")

        # 使用并行处理函数分析产品，设置每个产品的超时时间为300秒（5分钟）
        # 函数会自动过滤，只返回中/高风险产品
        product_analysis_data = analyze_products_in_parallel(
            detailed_products, timeout_per_product=300
        )

        # 获取中/高风险产品数量
        medium_high_risk_count = len(product_analysis_data)

        logging.info(
            f"所有产品并行分析完成！共分析{len(detailed_products)}个产品，其中中/高风险产品: {medium_high_risk_count}个"
        )
        print(
            f"\n所有产品并行分析完成！共分析 {len(detailed_products)} 个产品，其中中/高风险产品: {medium_high_risk_count}个"
        )

        # 发送数据到飞书表格
        print("\n" + "=" * 60)
        print("发送数据到飞书表格")
        print("-" * 60)
        logging.info("开始发送数据到飞书表格")

        # 使用增强版飞书表格写入函数
        print("正在发送产品分析数据到飞书表格...")
        logging.info("使用增强版飞书表格写入函数发送数据")

        # 使用新的增强版函数写入产品数据
        result = write_to_feishu_sheet(product_analysis_data)

        if result:
            success_msg = (
                f"成功发送分析结果到飞书表格（产品: {len(product_analysis_data)}条）"
            )
            logging.info(success_msg)
            print(f"✓ {success_msg}")
        else:
            error_msg = "数据发送失败"
            logging.error(error_msg)
            print(f"× {error_msg}")

            # 尝试使用旧版函数作为备份
            print("尝试使用旧版函数作为备份...")

            # 写入产品数据
            if product_analysis_data:
                logging.info(
                    f"使用旧版函数发送{len(product_analysis_data)}个产品分析结果"
                )
                result = write_to_feishu_sheet_legacy(
                    PRODUCT_SHEET_ID, product_analysis_data
                )
                if result:
                    print(f"✓ 成功发送{len(product_analysis_data)}个产品分析结果")
                else:
                    print(f"× 产品数据发送失败")

        completion_msg = "分析与数据发送完成！"
        logging.info("=" * 60)
        logging.info(completion_msg)
        logging.info("=" * 60)

        print("\n" + "=" * 60)
        print(completion_msg)
        print("=" * 60)
        print(f"\n日志已保存到: {log_filename}")

    except Exception as e:
        error_msg = f"发生错误: {str(e)}"
        logging.error(error_msg)
        logging.error(traceback.format_exc())
        print(f"\n{error_msg}")
        print(traceback.format_exc())


if __name__ == "__main__":
    main()
