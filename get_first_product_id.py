#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
获取第一个产品的ID和属性信息
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入主模块的函数
from 批量修改产品信息 import get_all_products, get_auth_headers, SHOP_NAME
import requests

def get_first_product_details():
    """获取第一个产品的详细信息"""
    print("获取第一个产品的详细信息...")
    
    # 获取产品列表
    products = get_all_products(status="active")
    
    if not products:
        print("没有找到任何产品")
        return None
    
    # 获取第一个产品的详细信息
    first_product = products[0]
    product_id = first_product["id"]
    
    print(f"第一个产品ID: {product_id}")
    print(f"产品标题: {first_product.get('title', 'N/A')}")
    
    # 获取详细信息
    url = f"https://{SHOP_NAME}.myshopify.com/admin/api/2023-04/products/{product_id}.json"
    headers = get_auth_headers()
    
    try:
        resp = requests.get(url, headers=headers, verify=False, timeout=10)
        
        if resp.status_code == 200:
            product_data = resp.json().get("product", {})
            options = product_data.get("options", [])
            variants = product_data.get("variants", [])
            
            print(f"\n产品详细信息:")
            print(f"  标题: {product_data.get('title', 'N/A')}")
            print(f"  Handle: {product_data.get('handle', 'N/A')}")
            print(f"  状态: {product_data.get('status', 'N/A')}")
            print(f"  产品类型: {product_data.get('product_type', 'N/A')}")
            
            print(f"\n属性信息 (共{len(options)}个):")
            for i, option in enumerate(options):
                print(f"  {i+1}. 名称: '{option.get('name', 'N/A')}'")
                print(f"     位置: {option.get('position', 'N/A')}")
                values = option.get('values', [])
                print(f"     值: {', '.join(values) if values else 'N/A'}")
                print()
            
            print(f"变体信息 (共{len(variants)}个):")
            for i, variant in enumerate(variants[:3]):  # 只显示前3个
                option_values = []
                for j in range(1, 4):
                    value = variant.get(f'option{j}')
                    if value:
                        option_values.append(f"option{j}: {value}")
                print(f"  变体 {i+1}: {', '.join(option_values)}")
                print(f"    价格: {variant.get('price', 'N/A')}")
                print(f"    SKU: {variant.get('sku', 'N/A')}")
                print()
            
            if len(variants) > 3:
                print(f"  ... 还有 {len(variants) - 3} 个变体")
            
            return product_id, options
        else:
            print(f"获取产品详情失败: HTTP {resp.status_code}")
            print(f"响应: {resp.text}")
            return None, None
            
    except Exception as e:
        print(f"获取产品详情时发生异常: {e}")
        return None, None

def main():
    """主函数"""
    print("获取第一个产品的ID和属性信息")
    print("=" * 50)
    
    try:
        product_id, options = get_first_product_details()
        
        if product_id and options:
            print(f"\n=== 总结 ===")
            print(f"产品ID: {product_id}")
            print(f"可用于测试的属性名称:")
            for i, option in enumerate(options):
                print(f"  '{option.get('name', 'N/A')}'")
            
            print(f"\n您可以使用产品ID {product_id} 来测试属性替换功能")
        else:
            print("无法获取产品信息")
        
    except KeyboardInterrupt:
        print("\n操作被用户中断")
    except Exception as e:
        print(f"操作过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
