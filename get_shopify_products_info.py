import requests
import json
import os
import time
import traceback
from datetime import datetime
import base64
import logging


# 加载配置文件
def load_config():
    """加载配置文件"""
    try:
        with open("config.json", "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception as e:
        logging.error(f"加载配置文件失败: {str(e)}")
        # 返回默认配置
        return {
            "shopify": {
                "shop_name": "9uii4g-sf",
                "api_key": "7413850cc553ff28f23a72b6208befcd",
                "api_password": "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",
                "rate_limit": 2,
            }
        }


# 加载配置
CONFIG = load_config()

# Shopify店铺详情
SHOP_NAME = CONFIG["shopify"]["shop_name"]  # 不带myshopify.com的版本
API_KEY = CONFIG["shopify"]["api_key"]
API_PASSWORD = CONFIG["shopify"]["api_password"]
# 获取自定义域名（如果有）
CUSTOM_DOMAIN = CONFIG["shopify"].get("custom_domain", "")


# Shopify API速率限制：每秒最多2次请求
class RateLimiter:
    """
    速率限制器，用于控制API请求速率
    """

    def __init__(self, max_calls_per_second=2):
        self.max_calls_per_second = max_calls_per_second
        self.call_timestamps = []

    def wait_if_needed(self):
        """
        检查是否需要等待以遵守速率限制
        """
        now = time.time()

        # 清理超过1秒的旧时间戳
        self.call_timestamps = [ts for ts in self.call_timestamps if now - ts < 1]

        # 如果当前请求数已达到限制，则等待
        if len(self.call_timestamps) >= self.max_calls_per_second:
            # 计算需要等待的时间
            oldest_timestamp = min(self.call_timestamps)
            wait_time = 1 - (now - oldest_timestamp)

            if wait_time > 0:
                logging.info(f"达到API速率限制，等待 {wait_time:.2f} 秒...")
                print(f"达到API速率限制，等待 {wait_time:.2f} 秒...")
                time.sleep(wait_time)

        # 添加当前请求的时间戳
        self.call_timestamps.append(time.time())


# 创建速率限制器实例
shopify_rate_limiter = RateLimiter(
    max_calls_per_second=CONFIG["shopify"].get("rate_limit", 2)
)


def get_auth_headers():
    """生成Basic认证头"""
    auth_str = f"{API_KEY}:{API_PASSWORD}"
    auth_bytes = auth_str.encode("ascii")
    auth_b64_bytes = base64.b64encode(auth_bytes)
    auth_b64_str = auth_b64_bytes.decode("ascii")
    return {
        "Content-Type": "application/json",
        "Authorization": f"Basic {auth_b64_str}",
    }


def get_all_products(status="active"):
    """
    获取产品列表，遵守Shopify API速率限制

    参数:
        status (str): 产品状态过滤，可选值: "any"(所有), "active"(活跃), "archived"(已归档), "draft"(草稿)

    返回:
        list: 产品列表
    """
    all_products = []
    headers = get_auth_headers()
    # 使用配置中的API版本（如果有），否则使用默认版本
    api_version = CONFIG.get("shopify", {}).get("api_version", "2023-04")

    # 添加状态过滤参数
    status_filter = f"&status={status}" if status != "any" else ""
    query_url = f"https://{SHOP_NAME}.myshopify.com/admin/api/{api_version}/products.json?limit=250{status_filter}"

    # 根据状态显示不同的消息
    status_display = {
        "any": "所有",
        "active": "活跃",
        "archived": "已归档",
        "draft": "草稿",
    }
    display_status = status_display.get(status, "所有")

    print(f"开始获取{display_status}产品...")
    logging.info(f"开始获取Shopify{display_status}产品...")

    while query_url:
        print(f"正在获取: {query_url}")
        logging.info(f"正在获取: {query_url}")

        # 使用速率限制器控制请求速率
        shopify_rate_limiter.wait_if_needed()

        try:
            # 添加 verify=False 参数禁用SSL验证
            response = requests.get(
                query_url, headers=headers, timeout=10, verify=False
            )

            # 检查是否达到API限制
            if response.status_code == 429:
                # 如果收到429状态码（Too Many Requests），等待并重试
                retry_after = int(response.headers.get("Retry-After", 5))
                print(f"达到API限制，等待 {retry_after} 秒后重试...")
                logging.warning(f"达到API限制，等待 {retry_after} 秒后重试...")
                time.sleep(retry_after)
                continue

            response.raise_for_status()  # 抛出其他HTTP错误

            data = response.json()
            products = data.get("products", [])
            all_products.extend(products)
            print(f"已获取 {len(all_products)} 个产品")
            logging.info(f"已获取 {len(all_products)} 个产品")

            # 检查API调用限制信息
            api_call_limit = response.headers.get("X-Shopify-Shop-Api-Call-Limit")
            if api_call_limit:
                logging.info(f"API调用限制: {api_call_limit}")
                # 如果接近限制，增加等待时间
                current, limit = map(int, api_call_limit.split("/"))
                if current > limit * 0.8:  # 如果已使用超过80%的配额
                    wait_time = 1.0  # 额外等待1秒
                    logging.warning(f"API调用接近限制，额外等待 {wait_time} 秒...")
                    time.sleep(wait_time)

            # 检查是否有下一页
            link_header = response.headers.get("Link")
            if link_header:
                links = link_header.split(",")
                next_link = None
                for link in links:
                    if 'rel="next"' in link:
                        next_link = link.split(";")[0].strip("<> ")
                        break
                query_url = next_link
            else:
                query_url = None

        except requests.exceptions.RequestException as e:
            error_msg = f"请求失败: {e}"
            print(error_msg)
            logging.error(error_msg)

            # 如果是连接错误或超时，等待后重试
            if isinstance(
                e, (requests.exceptions.ConnectionError, requests.exceptions.Timeout)
            ):
                wait_time = 5
                print(f"连接错误或超时，等待 {wait_time} 秒后重试...")
                logging.warning(f"连接错误或超时，等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
                continue
            else:
                break

    return all_products


def extract_product_links(products, status="active"):
    """
    提取产品链接

    参数:
        products (list): 产品列表
        status (str): 产品状态过滤，可选值: "any"(所有), "active"(活跃), "archived"(已归档), "draft"(草稿)

    返回:
        list: 产品链接列表
    """
    product_links = []
    filtered_count = 0

    for product in products:
        # 根据状态过滤产品
        if status != "any" and product.get("status") != status:
            continue

        filtered_count += 1
        product_id = product["id"]
        handle = product["handle"]
        title = product["title"]

        # 创建产品链接，使用自定义域名（如果有）
        domain = CUSTOM_DOMAIN if CUSTOM_DOMAIN else f"{SHOP_NAME}.myshopify.com"
        link = f"https://{domain}/products/{handle}"
        admin_link = f"https://{SHOP_NAME}.myshopify.com/admin/products/{product_id}"

        product_links.append(
            {"title": title, "product_link": link, "admin_link": admin_link}
        )

    # 根据状态显示不同的消息
    status_display = {
        "any": "所有",
        "active": "活跃",
        "archived": "已归档",
        "draft": "草稿",
    }
    display_status = status_display.get(status, "所有")

    print(f"找到 {filtered_count} 个{display_status}产品，共 {len(products)} 个产品")
    return product_links


def save_to_file(product_links, status="active"):
    """
    保存产品链接到本地文件

    参数:
        product_links (list): 产品链接列表
        status (str): 产品状态，用于文件名
    """
    if not product_links:
        print("没有产品链接可保存")
        return

    # 根据状态显示不同的文件名
    status_display = {
        "any": "all",
        "active": "active",
        "archived": "archived",
        "draft": "draft",
    }
    display_status = status_display.get(status, "all")

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"shopify_{display_status}_product_links_{timestamp}.txt"

    with open(filename, "w", encoding="utf-8") as file:
        for product in product_links:
            file.write(f"{product['product_link']}\n")
            # file.write(f"管理链接: {product['admin_link']}\n")
            # file.write(f"{'='*50}\n")

    print(f"{display_status.capitalize()}产品链接已保存到 {filename}")

    # # 同时保存为JSON以便程序使用
    # json_filename = f"shopify_active_product_links_{timestamp}.json"
    # with open(json_filename, 'w', encoding='utf-8') as file:
    #     json.dump(product_links, file, ensure_ascii=False, indent=2)

    # print(f"活跃产品链接也已保存为JSON格式到 {json_filename}")


def setup_logging():
    """设置日志配置"""
    # 创建logs目录（如果不存在）
    if not os.path.exists("logs"):
        os.makedirs("logs")

    # 生成日志文件名，包含时间戳
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f"logs/shopify_api_{timestamp}.log"

    # 配置日志格式
    log_format = "%(asctime)s [%(levelname)s] %(message)s"
    date_format = "%Y-%m-%d %H:%M:%S"

    # 设置root logger
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        datefmt=date_format,
        handlers=[
            logging.FileHandler(log_filename, encoding="utf-8"),
            logging.StreamHandler(),  # 同时输出到控制台
        ],
    )

    logging.info(f"日志已初始化，日志文件：{log_filename}")
    return log_filename


def main():
    # 初始化日志
    log_filename = setup_logging()

    try:
        # 添加产品状态选择
        print("\n请选择要获取的产品状态：")
        print("1. 所有产品")
        print("2. 只获取活跃的产品")
        print("3. 只获取已归档的产品")
        print("4. 只获取草稿产品")
        status_choice = input("请输入数字选择产品状态（默认为活跃产品）：").strip()

        status_map = {"1": "any", "2": "active", "3": "archived", "4": "draft"}

        product_status = "active"  # 默认为活跃产品
        if status_choice in status_map:
            product_status = status_map[status_choice]
            status_display = {
                "any": "所有",
                "active": "活跃的",
                "archived": "已归档的",
                "draft": "草稿",
            }
            print(f"将获取{status_display[product_status]}产品...")
            logging.info(f"选择获取{status_display[product_status]}产品")

        print(f"正在从Shopify店铺 {SHOP_NAME} 获取产品...")
        logging.info(f"正在从Shopify店铺 {SHOP_NAME} 获取产品...")

        products = get_all_products(status=product_status)

        if not products:
            error_msg = "未能获取到任何产品。请检查您的API密钥和密码是否正确。"
            print(error_msg)
            logging.error(error_msg)
            return

        success_msg = f"总共找到 {len(products)} 个产品"
        print(success_msg)
        logging.info(success_msg)

        product_links = extract_product_links(products, status=product_status)
        save_to_file(product_links, status=product_status)

        # 输出日志文件路径
        print(f"\n日志已保存到: {log_filename}")

    except Exception as e:
        error_msg = f"发生错误: {str(e)}"
        print(error_msg)
        logging.error(error_msg)
        logging.error(traceback.format_exc())  # 记录完整的堆栈跟踪
        print("请确保您的API密钥和密码正确，并且有适当的API访问权限。")
        print(f"\n日志已保存到: {log_filename}")


if __name__ == "__main__":
    main()
