import requests
import json
import time
import threading
import concurrent.futures
import re
from html import unescape


# 从主脚本导入必要的函数和变量
def sync_seo_for_products(
    products, config, rate_limiter, print_lock, result_collector, max_workers=1
):
    """
    批量同步产品的SEO标题和描述

    参数:
        products (list): 产品列表
        config (dict): 配置信息
        rate_limiter: 速率限制器
        print_lock: 打印锁
        result_collector: 结果收集器
        max_workers (int): 最大线程数
    """

    def get_auth_headers():
        """生成Basic认证头"""
        import base64

        api_key = config["shopify"]["api_key"]
        api_password = config["shopify"]["api_password"]
        auth_str = f"{api_key}:{api_password}"
        auth_bytes = auth_str.encode("ascii")
        auth_b64_bytes = base64.b64encode(auth_bytes)
        auth_b64_str = auth_b64_bytes.decode("ascii")
        return {
            "Content-Type": "application/json",
            "Authorization": f"Basic {auth_b64_str}",
        }

    def get_product_gid(product_id):
        return f"gid://shopify/Product/{product_id}"

    def get_product_metafields(product_gid):
        """获取产品SEO相关metafields"""
        shop_name = config["shopify"]["shop_name"]
        graphql_url = (
            f"https://{shop_name}.myshopify.com/admin/api/2023-10/graphql.json"
        )
        headers = get_auth_headers()
        query = {
            "query": f"""
            query {{
              product(id: \"{product_gid}\") {{
                metafields(first: 10, namespace: \"global\") {{
                  edges {{
                    node {{
                      id
                      key
                      value
                      type
                    }}
                  }}
                }}
              }}
            }}
            """
        }

        rate_limiter.wait_if_needed()
        try:
            resp = requests.post(
                graphql_url, headers=headers, json=query, verify=False, timeout=20
            )
            if resp.status_code == 200:
                data = resp.json()
                if data is None:
                    with print_lock:
                        print(f"[WARNING] 获取产品{product_gid} metafields返回空数据")
                    return {}

                metafields = {}
                product_data = data.get("data", {})
                if product_data is None:
                    with print_lock:
                        print(f"[WARNING] 获取产品{product_gid} metafields数据格式错误")
                    return {}

                product_info = product_data.get("product", {})
                if product_info is None:
                    with print_lock:
                        print(f"[WARNING] 获取产品{product_gid}信息失败")
                    return {}

                metafields_info = product_info.get("metafields", {})
                if metafields_info is None:
                    return {}

                edges = metafields_info.get("edges", [])
                if edges is None:
                    return {}

                for edge in edges:
                    if edge and "node" in edge:
                        node = edge["node"]
                        if node and "key" in node:
                            metafields[node["key"]] = node
                return metafields
            else:
                with print_lock:
                    print(
                        f"[WARNING] 获取产品{product_gid} metafields失败: {resp.status_code} - {resp.text}"
                    )
        except Exception as e:
            with print_lock:
                print(f"[WARNING] 获取产品{product_gid} metafields失败: {e}")
        return {}

    def update_product_graphql(
        product_gid, update_fields, metafields=None, max_retries=3
    ):
        """使用GraphQL更新产品，带重试机制"""
        shop_name = config["shopify"]["shop_name"]
        graphql_url = (
            f"https://{shop_name}.myshopify.com/admin/api/2023-10/graphql.json"
        )
        headers = get_auth_headers()
        input_obj = {"id": product_gid}
        input_obj.update(update_fields)
        if metafields:
            input_obj["metafields"] = metafields
        mutation = {
            "query": """
            mutation productUpdate($input: ProductInput!) {
              productUpdate(input: $input) {
                product { id title }
                userErrors { field message }
              }
            }
            """,
            "variables": {"input": input_obj},
        }

        for attempt in range(max_retries):
            rate_limiter.wait_if_needed()
            try:
                resp = requests.post(
                    graphql_url,
                    headers=headers,
                    json=mutation,
                    verify=False,
                    timeout=10,
                )
                if resp.status_code == 200:
                    data = resp.json()
                    if data is None:
                        if attempt < max_retries - 1:
                            with print_lock:
                                print(
                                    f"[WARNING] 产品{product_gid}更新返回空数据，重试 {attempt + 1}/{max_retries}"
                                )
                            time.sleep(3 + (2**attempt))  # 更长的延迟
                            continue
                        else:
                            with print_lock:
                                print(f"[ERROR] 产品{product_gid}更新返回空数据")
                                print(f"[DEBUG] 响应内容: {resp.text}")
                            return False

                    # 检查是否有GraphQL错误
                    if "errors" in data:
                        errors = data["errors"]
                        # 检查是否是可重试的错误
                        is_retryable = any(
                            error.get("extensions", {}).get("code")
                            in ["INTERNAL_SERVER_ERROR", "TIMEOUT", "THROTTLED"]
                            for error in errors
                        )

                        if is_retryable and attempt < max_retries - 1:
                            with print_lock:
                                print(
                                    f"[WARNING] 产品{product_gid} GraphQL临时错误，重试 {attempt + 1}/{max_retries}"
                                )
                            time.sleep(5 + (2**attempt))  # 更长的延迟：5秒 + 指数退避
                            continue
                        else:
                            with print_lock:
                                print(
                                    f"[ERROR] 产品{product_gid} GraphQL错误: {errors}"
                                )
                            return False

                    data_content = data.get("data", {})
                    if data_content is None:
                        if attempt < max_retries - 1:
                            with print_lock:
                                print(
                                    f"[WARNING] 产品{product_gid}更新数据格式错误，重试 {attempt + 1}/{max_retries}"
                                )
                            time.sleep(3 + (2**attempt))
                            continue
                        else:
                            with print_lock:
                                print(f"[ERROR] 产品{product_gid}更新数据格式错误")
                                print(f"[DEBUG] 完整响应: {data}")
                            return False

                    product_update = data_content.get("productUpdate", {})
                    if product_update is None:
                        if attempt < max_retries - 1:
                            with print_lock:
                                print(
                                    f"[WARNING] 产品{product_gid}更新响应格式错误，重试 {attempt + 1}/{max_retries}"
                                )
                            time.sleep(3 + (2**attempt))
                            continue
                        else:
                            with print_lock:
                                print(f"[ERROR] 产品{product_gid}更新响应格式错误")
                                print(f"[DEBUG] data内容: {data_content}")
                            return False

                    user_errors = product_update.get("userErrors", [])
                    if user_errors:
                        with print_lock:
                            print(f"[ERROR] 产品{product_gid}更新失败: {user_errors}")
                        return False

                    # 检查是否真的更新成功
                    updated_product = product_update.get("product")
                    if updated_product is None:
                        if attempt < max_retries - 1:
                            with print_lock:
                                print(
                                    f"[WARNING] 产品{product_gid}更新后未返回产品信息，重试 {attempt + 1}/{max_retries}"
                                )
                            time.sleep(3 + (2**attempt))
                            continue
                        else:
                            with print_lock:
                                print(f"[ERROR] 产品{product_gid}更新后未返回产品信息")
                                print(f"[DEBUG] productUpdate内容: {product_update}")
                            return False

                    return True
                else:
                    if attempt < max_retries - 1:
                        with print_lock:
                            print(
                                f"[WARNING] 产品{product_gid} HTTP错误 {resp.status_code}，重试 {attempt + 1}/{max_retries}"
                            )
                        time.sleep(3 + (2**attempt))
                        continue
                    else:
                        with print_lock:
                            print(
                                f"[ERROR] 产品{product_gid} GraphQL更新失败: {resp.status_code} - {resp.text}"
                            )
                        return False
            except Exception as e:
                if attempt < max_retries - 1:
                    with print_lock:
                        print(
                            f"[WARNING] 产品{product_gid} 请求异常，重试 {attempt + 1}/{max_retries}: {e}"
                        )
                    time.sleep(3 + (2**attempt))
                    continue
                else:
                    with print_lock:
                        print(f"[ERROR] 产品{product_gid} GraphQL请求异常: {e}")
                        import traceback

                        print(f"[DEBUG] 异常详情: {traceback.format_exc()}")
                    return False

        return False

    def clean_html_for_seo(html_content, max_length=160):
        """
        清理HTML内容用于SEO描述

        参数:
            html_content (str): HTML内容
            max_length (int): 最大长度，默认160字符（SEO描述推荐长度）

        返回:
            str: 清理后的纯文本
        """
        if not html_content:
            return ""

        # 移除HTML标签
        clean_text = re.sub(r"<[^>]+>", "", html_content)
        # 解码HTML实体
        clean_text = unescape(clean_text)
        # 标准化空白字符
        clean_text = re.sub(r"\s+", " ", clean_text).strip()

        # 移除特殊字符和控制字符
        clean_text = re.sub(r"[\x00-\x1f\x7f-\x9f]", "", clean_text)
        # 移除多余的标点符号
        clean_text = re.sub(r"[^\w\s\.\,\!\?\-\(\)]", "", clean_text)
        # 再次标准化空白字符
        clean_text = re.sub(r"\s+", " ", clean_text).strip()

        # 截断到指定长度
        if len(clean_text) > max_length:
            # 在单词边界截断
            truncated = clean_text[:max_length]
            last_space = truncated.rfind(" ")
            if last_space > max_length * 0.8:  # 如果最后一个空格位置合理
                clean_text = truncated[:last_space] + "..."
            else:
                clean_text = truncated + "..."

        return clean_text

    def sync_single_product_seo(product, product_index=None, total_products=None):
        """
        同步单个产品的SEO信息

        参数:
            product (dict): 产品信息
            product_index (int): 产品索引
            total_products (int): 总产品数

        返回:
            tuple: (result_type, product_info)
        """
        try:
            product_id = product["id"]
            product_gid = get_product_gid(product_id)

            # 获取产品详细信息
            shop_name = config["shopify"]["shop_name"]
            url = f"https://{shop_name}.myshopify.com/admin/api/2023-04/products/{product_id}.json"
            headers = get_auth_headers()

            rate_limiter.wait_if_needed()
            resp = requests.get(url, headers=headers, verify=False, timeout=10)
            if resp.status_code != 200:
                with print_lock:
                    print(
                        f"获取产品{product_id}详情失败: {resp.status_code} - {resp.text}"
                    )
                return "failed", {
                    "id": product_id,
                    "title": product.get("title", f"ID: {product_id}"),
                }

            response_data = resp.json()
            if response_data is None:
                with print_lock:
                    print(f"获取产品{product_id}详情返回空数据")
                return "failed", {
                    "id": product_id,
                    "title": product.get("title", f"ID: {product_id}"),
                }

            product_data = response_data.get("product", {})
            if product_data is None:
                with print_lock:
                    print(f"获取产品{product_id}详情数据格式错误")
                return "failed", {
                    "id": product_id,
                    "title": product.get("title", f"ID: {product_id}"),
                }
            title = product_data.get("title", "")
            body_html = product_data.get("body_html", "")

            if not title:
                with print_lock:
                    print(f"产品{product_id}没有标题，跳过")
                return "skipped", {"id": product_id, "title": "无标题"}

            # 获取现有的metafields
            metafields_data = get_product_metafields(product_gid)

            # 准备更新数据
            update_fields = {}
            metafields = []
            need_update = False

            # 处理SEO标题和描述 - 强制同步，优先使用内置SEO字段
            seo_title = title

            # 检查现有SEO描述是否过长（可能导致问题）
            description_tag_node = metafields_data.get("description_tag")
            has_long_description = False

            if description_tag_node and description_tag_node.get("value"):
                existing_desc = description_tag_node["value"]
                if len(existing_desc) > 500:  # 如果现有描述超过500字符
                    has_long_description = True
                    with print_lock:
                        print(
                            f"[INFO] 产品{product_id}现有SEO描述过长({len(existing_desc)}字符)，将先清理"
                        )

            # 使用产品的内置SEO字段（更稳定）
            if "seo" not in update_fields:
                update_fields["seo"] = {}

            update_fields["seo"]["title"] = seo_title
            need_update = True

            if body_html:
                seo_description = clean_html_for_seo(body_html)
                update_fields["seo"]["description"] = seo_description

            if need_update:
                # 对于有长描述的产品，先尝试清理现有SEO数据
                if has_long_description:
                    with print_lock:
                        print(f"[INFO] 产品{product_id}有过长SEO描述，先清理现有数据")

                    # 先清理SEO数据（只使用内置字段，避免metafields复杂性）
                    clear_fields = {"seo": {"title": "", "description": ""}}
                    clear_success = update_product_graphql(
                        product_gid, clear_fields, None
                    )

                    if clear_success:
                        with print_lock:
                            print(
                                f"[INFO] 产品{product_id}SEO数据清理成功，等待5秒后重新设置"
                            )
                        time.sleep(5)
                    else:
                        with print_lock:
                            print(
                                f"[WARNING] 产品{product_id}SEO数据清理失败，继续尝试直接更新"
                            )

                # 执行更新（只使用内置SEO字段）
                success = update_product_graphql(product_gid, update_fields, None)

                if success:
                    # 生成产品链接
                    custom_domain = config["shopify"].get("custom_domain", "")
                    shop_name = config["shopify"]["shop_name"]
                    product_handle = product_data.get("handle", "")
                    product_link = (
                        f"https://{custom_domain}/products/{product_handle}"
                        if custom_domain
                        else f"https://{shop_name}.myshopify.com/products/{product_handle}"
                    )

                    # 显示进度信息
                    progress_info = ""
                    if product_index is not None and total_products is not None:
                        progress_info = f" - 进度: {product_index+1}/{total_products}"

                    with print_lock:
                        print(
                            f"✓ 产品 {title}（ID: {product_id}）SEO信息已同步{progress_info}"
                        )
                        print(f"  产品链接: {product_link}")

                    return "updated", {
                        "id": product_id,
                        "title": title,
                        "link": product_link,
                    }
                else:
                    with print_lock:
                        print(f"× 产品 {title}（ID: {product_id}）SEO同步失败")
                    return "failed", {"id": product_id, "title": title}
            else:
                with print_lock:
                    print(
                        f"- 产品 {title}（ID: {product_id}）SEO信息已是最新，无需更新"
                    )
                return "skipped", {"id": product_id, "title": title}

        except Exception as e:
            with print_lock:
                print(f"处理产品SEO同步时发生错误: {str(e)}")
            return "failed", {
                "id": product.get("id", "unknown"),
                "title": product.get("title", "unknown"),
            }

    # 执行批量处理
    total_products = len(products)
    print(f"开始同步 {total_products} 个产品的SEO信息...")

    start_time = time.time()

    if max_workers > 1:
        # 多线程模式
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_index = {}
            for i, product in enumerate(products):
                future = executor.submit(
                    sync_single_product_seo, product, i, total_products
                )
                future_to_index[future] = i

            # 处理完成的任务
            for future in concurrent.futures.as_completed(future_to_index):
                try:
                    result_type, product_info = future.result()
                    result_collector.add_result(result_type, product_info)

                    # 定期显示进度
                    stats = result_collector.get_stats()
                    if (stats["total"] % 10 == 0) or (stats["total"] == total_products):
                        elapsed = time.time() - start_time
                        with print_lock:
                            print(
                                f"\n当前进度: {stats['total']}/{total_products} "
                                f"(成功: {stats['updated']}, 跳过: {stats['skipped']}, "
                                f"失败: {stats['failed']}) - 耗时: {elapsed:.1f}秒"
                            )
                except Exception as e:
                    with print_lock:
                        print(f"处理SEO同步任务时发生错误: {str(e)}")
                    result_collector.add_result("failed")
    else:
        # 单线程模式
        for i, product in enumerate(products):
            result_type, product_info = sync_single_product_seo(
                product, i, total_products
            )
            result_collector.add_result(result_type, product_info)
