[Skip to main content](https://support.google.com/merchants/answer/7052112?visit_id=638823643727659825-**********&hl=en&rd=1#hcfe-content)

# Product data specification

Use this guide to format your product information for Merchant Center. Google uses this data to make sure that it's matched to the right queries. Sharing your product data in the correct format is important for creating successful ads and free listings for your products.

#### In this article

- [Before you begin](https://support.google.com/merchants/answer/7052112?visit_id=638823643727659825-**********&hl=en&rd=1#before_you_begin)
- [Definitions](https://support.google.com/merchants/answer/7052112?visit_id=638823643727659825-**********&hl=en&rd=1#Definitions)

### Product data attributes:

- [Basic product data](https://support.google.com/merchants/answer/7052112?visit_id=638823643727659825-**********&hl=en&rd=1#basic_product_data)
- [Price and availability](https://support.google.com/merchants/answer/7052112?visit_id=638823643727659825-**********&hl=en&rd=1#price_and_availability)
- [Product category](https://support.google.com/merchants/answer/7052112?visit_id=638823643727659825-**********&hl=en&rd=1#product_category)
- [Product identifiers](https://support.google.com/merchants/answer/7052112?visit_id=638823643727659825-**********&hl=en&rd=1#product_identifiers)
- [Detailed product description](https://support.google.com/merchants/answer/7052112?visit_id=638823643727659825-**********&hl=en&rd=1#detailed_product_description)
- [Shopping campaigns and other configurations](https://support.google.com/merchants/answer/7052112?visit_id=638823643727659825-**********&hl=en&rd=1#shopping_campaigns_and_other)
- [Marketplaces](https://support.google.com/merchants/answer/7052112?visit_id=638823643727659825-**********&hl=en&rd=1#marketplaces)
- [Destinations](https://support.google.com/merchants/answer/7052112?visit_id=638823643727659825-**********&hl=en&rd=1#destinations)
- [Shipping](https://support.google.com/merchants/answer/7052112?visit_id=638823643727659825-**********&hl=en&rd=1#shipping_and_returns)
- [Tax](https://support.google.com/merchants/answer/7052112?visit_id=638823643727659825-**********&hl=en&rd=1#tax)

* * *

## Before you begin

## Other requirements

In addition to this product data specification, your product data must also meet the following requirements:

- [**Shopping ads policies**](https://support.google.com/merchants/answer/6149970)
- [**Landing page requirements**](https://support.google.com/merchants/answer/4752265)
- **Tax rate data requirements**
- **Shipping rate data requirements**
- [**Checkout requirements and best practices**](https://support.google.com/merchants/answer/9158778)
- [**Currency and language requirements**](https://support.google.com/merchants/answer/160637)

## Formatting your product data

Use English when submitting the names of attributes and the values for attributes that use supported values. For example, the condition `[condition]` attribute uses the supported values `new`, `refurbished`, and `used`, which must be submitted in English in order for the system to read them.

For all attributes that do not use supported values, but rather allow for free form text, such as the title `[title]` or description `[description]` attributes, be sure to use the same language for all attributes in a feed. Use an underscore when submitting an attribute name with multiple words (for example, `image_link`). [Learn more about how to submit attributes and attribute values](https://support.google.com/merchants/answer/10668075)

* * *

## Definitions

- **Product**: This is the actual product that potential customers search for on Google.
- **Item**: This is a product that has been added to your product data, either in a text feed, XML feed, or API. For example, an item is one line in your text feed.
- **Variant**: These are specific versions of a product that comes in different variations. For example, a shirt that comes in different sizes has size variants.

![Required](https://storage.googleapis.com/support-kms-prod/ncoEh9hPBwKEuXOUKRffUwCyLKZVt7PeCt6w)**Required**: Submit this attribute. If you don't, your product won't be able to serve in ads and free listings.

![This icon represents whether the sourced content is dependent where the product attribute is used](https://storage.googleapis.com/support-kms-prod/wjVVtCqvAHd5rSOyJWrN0YpmnUV3ritl4686)**It depends**: You may or may not need to submit this attribute depending on the product or the countries in which your products show.

![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional**: You can submit this attribute if you want to help boost your product's performance.

## Basic product data

The product information you submit using these attributes is the foundation for creating successful ads and free listings for your products. Make sure everything you submit is of the quality you'd show to a customer.

| **Attribute and format** | **Minimum requirements at a glance** |
| --- | --- |
| [ID `[id]`](https://support.google.com/merchants/answer/6324405)<br>Your product’s unique identifier<br>![Required](https://storage.googleapis.com/support-kms-prod/ncoEh9hPBwKEuXOUKRffUwCyLKZVt7PeCt6w)**Required**<br>**Example**<br>`A2B4`<br>**Syntax**<br>Max 50 characters<br>**Schema.org property**: Yes (Learn more about [Merchant listing (Product, Offer) structured data](https://developers.google.com/search/docs/appearance/structured-data/merchant-listing) on Google Search Central) | - Use a unique value for each product.<br>- Use the product's SKU where possible.<br>- Keep the ID the same when updating your data.<br>- Use only valid unicode characters.<br>- Use the same ID for the same product across countries or languages. |
| [Title `[title]`](https://support.google.com/merchants/answer/6324415)<br>or<br>[Structured title `[structured_title]`](https://support.google.com/merchants/answer/6324415)<br>Your product’s name<br>![Required](https://storage.googleapis.com/support-kms-prod/ncoEh9hPBwKEuXOUKRffUwCyLKZVt7PeCt6w)**Required**<br>Example ( [Title `[title]`](https://support.google.com/merchants/answer/6324415)):<br>Mens Pique Polo Shirt<br>Example ( [Structured title `[structured_title]`](https://support.google.com/merchants/answer/6324415)): `trained_algorithmic_media:"Stride & Conquer: Original Google Men's Blue & Orange Power Shoes (Size 8)"`<br>**Syntax**<br>[Title `[title]`](https://support.google.com/merchants/answer/6324415): Plain text.Max 150 characters<br>[Structured title `[structured_title]`](https://support.google.com/merchants/answer/6324415): 2 sub-attributes:<br>- Digital source type `[digital_source_type]` ( **Optional**): This sub-attribute supports 2 values:<br>  <br>   <br>  - Default `[default]`: Specifies that the title provided using the content `[content]` sub-attribute was **not** created using generative AI.<br>  - Trained algorithmic media `[trained_algorithmic_media]`. Specifies that the title provided using the content `[content]` sub-attribute was created using Generative AI.<br>If no value is specified, the Default \[default\] value is used.<br>- Content `[content]` ( **Required**): The title text. Max 150 characters.<br>**Schema.org property** ``:<br>[Title `[title]`](https://support.google.com/merchants/answer/6324415): Yes (Learn more about [Merchant listing (Product, Offer) structured data](https://developers.google.com/search/docs/appearance/structured-data/merchant-listing) on Google Search Central) ``<br>[Structured title `[structured_title]`](https://support.google.com/merchants/answer/6324415): No | - Use one of the title `[title]` and structured title `[structured_title]` attributes to clearly identify the product you are selling.<br>- For titles created using generative AI, use the structured title `[structured_title]` attribute, otherwise use the title `[title]` attribute.<br>- Accurately describe your product and match the title from your landing page.<br>- Don’t include promotional text like "free shipping," all capital letters, or gimmicky foreign characters.<br>For variants:<br>- Include distinguishing features such as color or size.<br>For mobile devices:<br>- Include “with contract” if sold with a contract.<br>- For the United States, include “with payment plan” if sold in installments.<br>For Russia:<br>- For books and other information products, include the age rating at the beginning of the title. |
| [Description `[description]`](https://support.google.com/merchants/answer/6324468)<br>or<br>[Structured description `[structured_description]`](https://support.google.com/merchants/answer/6324468)<br>Your product’s description<br>![Required](https://storage.googleapis.com/support-kms-prod/ncoEh9hPBwKEuXOUKRffUwCyLKZVt7PeCt6w)**Required**<br>**Example** ( [description `[description]`](https://support.google.com/merchants/answer/6324468)):<br>Made from 100% organic cotton, this classic red men’s polo has a slim fit and signature logo embroidered on the left chest. Machine wash cold; imported.<br>Example ( [structured description `[structured_description]`](https://support.google.com/merchants/answer/6324468)):<br>`trained_algorithmic_media:"Transform your TV with the effortless power of Google Chromecast. This sleek device discreetly connects to your television, unlocking a world of wireless streaming and mirroring possibilities. From movies and TV shows to photos and presentations, cast your favorite content directly to the big screen with its integrated HDMI connector."`<br>**Syntax**<br>[Description `[description]`](https://support.google.com/merchants/answer/6324468): Plain Text. Max 5000 characters<br>[Structured description `[structured_description]`](https://support.google.com/merchants/answer/6324468): 2 sub-attributes:<br>- Digital source type `[digital_source_type]` ( **Optional**): This sub-attribute supports 2 values:<br>  <br>   <br>  - Default `[default]`: Specifies that the title provided using the content \[content\] sub-attribute was **not** created using generative AI.<br>  - Trained algorithmic media `[trained_algorithmic_media]`. Specifies that the title provided using the content `[content]` sub-attribute was created using Generative AI.<br>If no value is specified, the Default \[default\] value is used.<br>Content `[content]` ( **Required**): The description text. Max 5000 characters<br>**Schema.org property** ``:<br>[Description `[description]`](https://support.google.com/merchants/answer/6324468): Yes (Learn more about [Merchant listing (Product, Offer) structured data](https://developers.google.com/search/docs/appearance/structured-data/merchant-listing) on Google Search Central)<br>[Structured description `[structured_description]`](https://support.google.com/merchants/answer/6324468): No | - Use one of the description `[description]` and structured description `[structured_description]` attributes to accurately describe your product and match the description from your landing page.<br>- For descriptions created using generative AI, use the structured description `[structured_description] ` attribute, otherwise use the description `[description]` attribute.<br>- Don’t include promotional text like "free shipping," all capital letters, or gimmicky foreign characters.<br>- Include only information about the product. Don’t include links to your store, sales information, details about competitors, other products, or accessories.<br>- Use formatting (for example, line breaks, lists, or italics) to format your description. |
| [Link `[link]`](https://support.google.com/merchants/answer/6324416)<br>Your product’s landing page<br>![Required](https://storage.googleapis.com/support-kms-prod/ncoEh9hPBwKEuXOUKRffUwCyLKZVt7PeCt6w)**Required**<br>**Example**<br>`http://www.example.com/asp/sp.asp?cat=12&id=1030`<br>**Schema.org property** ``: Yes (Learn more about [Merchant listing (Product, Offer) structured data](https://developers.google.com/search/docs/appearance/structured-data/merchant-listing) on Google Search Central) `` | - Use your verified domain name.<br>- Start with `http` or `https`.<br>- Use an encoded URL that complies with RFC 2396 or RFC 1738.<br>- Don't link to an interstitial page unless legally required. |
| [Image link `[image_link]`](https://support.google.com/merchants/answer/12472547)<br>The URL of your product’s main image<br>![Required](https://storage.googleapis.com/support-kms-prod/ncoEh9hPBwKEuXOUKRffUwCyLKZVt7PeCt6w)**Required**<br>**Example**<br>`http:// www.example.com/image1.jpg`<br>**Schema.org property** ``: Yes (Learn more about [Merchant listing (Product, Offer) structured data](https://developers.google.com/search/docs/appearance/structured-data/merchant-listing) on Google Search Central) `` | For the image URL:<br>- Link to the main image of your product.<br>- Start with `http` or `https`.<br>- Use an encoded URL that complies with RFC 2396 or RFC 1738.<br>- Make sure the URL can be crawled by Google (robots.txt configuration allowing Googlebot and Googlebot-image).<br>For the image:<br>- Accurately display the product.<br>- Use an accepted format: JPEG (.jpg/.jpeg), WebP (.webp), PNG (.png), non-animated GIF (.gif), BMP (.bmp), and TIFF (.tif/.tiff).<br>- Don't scale up an image or submit a thumbnail.<br>- Don't include promotional text, watermarks, or borders.<br>- Don't submit a placeholder or a generic image.<br>- All images created using generative AI must contain meta data indicating that the image was AI-generated (for example, the IPTC [`DigitalSourceType`](https://cv.iptc.org/newscodes/digitalsourcetype/) [`TrainedAlgorithmicMedia`](https://cv.iptc.org/newscodes/digitalsourcetype/trainedAlgorithmicMedia) metadata tag). Don't remove embedded metadata tags such as the IPTC `DigitalSourceType` property from images created using generative AI tools, for example [Product Studio](https://support.google.com/merchants/answer/13708167). The following IPTC NewsCodes specify the type of digital source that was used to create the image, and should be preserved:<br>  <br>  <br>  <br>- [`TrainedAlgorithmicMedia`](https://cv.iptc.org/newscodes/digitalsourcetype/trainedAlgorithmicMedia): The image was created using a model derived from sampled content.<br>- [`CompositeSynthetic`](https://cv.iptc.org/newscodes/digitalsourcetype/compositeSynthetic): The image is a composite that includes synthetic elements.<br>- [`AlgorithmicMedia`](https://cv.iptc.org/newscodes/digitalsourcetype/algorithmicMedia): The image was created purely by an algorithm not based on any sampled training data (for example, an image created by software using a mathematical formula). |
| [Additional image link `[additional_image_link]`](https://support.google.com/merchants/answer/12472826)<br>The URL of an additional image for your product<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional**<br>**Example**<br>`http://www.example.com/image1.jpg`<br>**Syntax**<br>Max 2000 characters<br>**Schema.org property**: No | - Meet the requirements for the image link `[image_link]` attribute with these exceptions:<br>   <br>  - The image can include product staging and show the product in use.<br>  - Graphics or illustrations can be included.<br>- Submit up to 10 additional product images by including this attribute multiple times.<br>- All images created using generative AI must contain meta data indicating that the image was AI-generated (for example, the IPTC [`DigitalSourceType`](https://cv.iptc.org/newscodes/digitalsourcetype/) [`TrainedAlgorithmicMedia`](https://cv.iptc.org/newscodes/digitalsourcetype/trainedAlgorithmicMedia) metadata tag). Don't remove embedded metadata tags such as the IPTC `DigitalSourceType` property from images created using generative AI tools, for example [Product Studio](https://support.google.com/merchants/answer/13708167). The following IPTC NewsCodes specify the type of digital source that was used to create the image, and should be preserved:<br>  <br>  <br>  <br>- [`TrainedAlgorithmicMedia`](https://cv.iptc.org/newscodes/digitalsourcetype/trainedAlgorithmicMedia): The image was created using a model derived from sampled content.<br>- [`CompositeSynthetic`](https://cv.iptc.org/newscodes/digitalsourcetype/compositeSynthetic): The image is a composite that includes synthetic elements.<br>- [`AlgorithmicMedia`](https://cv.iptc.org/newscodes/digitalsourcetype/algorithmicMedia): The image was created purely by an algorithm not based on any sampled training data (for example, an image created by software using a mathematical formula). |
| [3D model link](https://support.google.com/merchants/answer/13674896) `[virtual_model_link]`<br>Additional link to show a 3D model of your product.<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional** (available only in the US)<br>Note: This attribute is only available in the classic experience of Merchant Center<br>**Example**<br>`https://www.google.com/products/xyz.glb`<br>**Syntax/type**<br>URL (Must start with "http://" or "https://")<br>Up to 2000 characters | - **Use a 3D model**. Your file shouldn’t exceed 15MB. Textures in the file can be up to 2K (4K isn’t supported).<br>- **Provide a valid URL in your product data**. The link should point to a .gltf, or .glb file.<br>- **Review your 3D model**. You can use [a validation tool](https://github.khronos.org/glTF-Validator/) to verify if your 3D model works properly. |
| [Mobile link `[mobile_link]`](https://support.google.com/merchants/answer/12472641)<br>Your product’s mobile-optimized landing page when you have a different URL for mobile and desktop traffic<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional**<br>**Example**<br>**`http://www.m.example.com/asp/ sp.asp?cat=12 id=1030`**<br>**Syntax**<br>**Max 2000 alphanumeric characters**<br>**Schema.org property: No** | - Meet the requirements for the link `[link]` attribute. |

## Price and availability

These attributes define the price and availability for your products. This information is shown to potential customers in ads and free listings. If your products' prices and availability change often, you'll need to let us know in order to show your products. [Check out these tips for keeping your product information fresh](https://support.google.com/merchants/answer/188489)

| **Attribute and format** | **Minimum requirements at a glance** |
| --- | --- |
| [Availability `[availability]`](https://support.google.com/merchants/answer/12472827)<br>Your product's availability<br>![Required](https://storage.googleapis.com/support-kms-prod/ncoEh9hPBwKEuXOUKRffUwCyLKZVt7PeCt6w)**Required**<br>**Example**<br>`in_stock`<br>**Supported values**<br>- In stock `[in_stock]`<br>- Out of stock ` [out_of_stock]`<br>- Preorder `[preorder]`<br>- Backorder `[backorder]`<br>**Schema.org property**: Yes (Learn more about [Merchant listing (Product, Offer) structured data](https://developers.google.com/search/docs/appearance/structured-data/merchant-listing) on Google Search Central) | - Accurately submit the product's availability and match the availability from your landing page and checkout pages.<br>- Provide the [availability date `[availability_date]`](https://support.google.com/merchants/answer/6324470) attribute if you submit preorder `[preorder]` or backorder `[backorder]` as the availability value. |
| [Availability date `[availability_date]`](https://support.google.com/merchants/answer/6324470)<br>![Required](https://storage.googleapis.com/support-kms-prod/ncoEh9hPBwKEuXOUKRffUwCyLKZVt7PeCt6w)**Required** if product availability is set to `preorder`<br>The date a preordered product becomes available for delivery<br>**Example**<br>(For UTC+1)<br>`2016-02-24T11:07+0100`<br>**Syntax**<br>- Max 25 alphanumeric characters<br>- ISO 8601<br>  - `YYYY-MM-DDThh:mm [+hhmm]`<br>  - `YYYY-MM-DDThh:mmZ`<br>**Schema.org property**: Yes (Learn more about [Merchant listing (Product, Offer) structured data](https://developers.google.com/search/docs/appearance/structured-data/merchant-listing) on Google Search Central) | - Use this attribute if your product's availability is set to `preorder`. Provide a value up to one year in the future.<br>- The availability date should also be added to the product’s landing page and be clear to your customers (for example, “May 6, 2023”).<br>  - If an exact date can’t be provided, you can use an estimated date (for example, “May 2023”). |
| [Cost of goods sold `[cost_of_goods_sold]`](https://support.google.com/merchants/answer/********)<br>Your product’s description<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional**<br>The costs associated with the sale of a particular product as defined by the accounting convention you set up. These costs may include material, labor, freight, or other overhead expenses. By submitting the COGS for your products, you gain insights about other metrics, such as your gross margin and the amount of revenue generated by your ads and free listings.<br>**Example**<br>23.00 USD<br>**Syntax**<br>- ISO 4217 codes<br>- Use '.' rather than ',' to indicate a decimal point<br>- Numeric<br>**Schema.org property**: No | - The currency must be in the ISO 4217 format. For example, USD for US dollars.<br>- The decimal point must be a period (.). For example, 10.00 USD. |
| [Expiration date `[expiration_date]`](https://support.google.com/merchants/answer/********)<br>The date that your product should stop showing<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional**<br>**Example**<br>(For UTC+1)<br>`2016-07-11T11:07+0100`<br>**Syntax**<br>- Max 25 alphanumeric characters<br>- ISO 8601<br>  - `YYYY-MM-DDThh:mm [+hhmm]`<br>  - `YYYY-MM-DDThh:mmZ`<br>**Schema.org property**: No | - Use a date less than 30 days in the future.<br>- Note that a latency of several hours may occur. |
| [Price `[price]`](https://support.google.com/merchants/answer/12471842)<br>Your products price<br>![Required](https://storage.googleapis.com/support-kms-prod/ncoEh9hPBwKEuXOUKRffUwCyLKZVt7PeCt6w)**Required**<br>**Example**<br>15.00 USD<br>**Syntax**<br>- Numeric<br>- ISO 4217<br>**Schema.org property**: Yes  (Learn more about [Merchant listing (Product, Offer) structured data](https://developers.google.com/search/docs/appearance/structured-data/merchant-listing) on Google Search Central) | - Accurately submit the product's price and currency, and match with the price from your landing page and at checkout.<br>- Make sure that your landing page and the checkout pages include the price in the currency of the target country in a place that's straightforward to find.<br>- Ensure that the product can be purchased online for the submitted price.<br>- Make sure that any customer can buy the product for the submitted price, without having to sign up for a membership program (free or paid).<br>  - In countries where the [loyalty program `[loyalty_program]`](https://support.google.com/merchants/answer/12827255) attribute is available, use the price `[price]` sub-attribute within the loyalty program `[loyalty_program]` attribute when submitting the member price for both free and paid memberships. Don’t use price `[price]` or sale price `[sale_price]` to submit member prices.<br>- Don't submit a price of 0 (a price of 0 is allowed for mobile devices sold with a contract only).<br>- For products sold in bulk quantities, bundles, or multipacks.<br>  - Submit the total price of the minimum purchasable quantity, bundle, or multipack.<br>- For the US and Canada:<br>  - Don't include tax in the price.<br>- For all other countries:<br>  - Include value added tax (VAT) or Goods and Services Tax (GST) in the price.<br>- For additional options to submit price-related information, check the following attributes:<br>  - Unit pricing measure `[unit_pricing_measure]`<br>  - Unit pricing base measure `[unit_pricing_base_measure]`<br>  - Sale price `[sale_price]`<br>  - Subscription cost `[subscription_cost]`<br>  - Installment `[installment]`<br>  - Loyalty program `[loyalty_program]` |
| [Sale price `[sale_price]`](https://support.google.com/merchants/answer/12471623)<br>Your product's sale price<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional**<br>**Example**<br>15.00 USD<br>**Syntax**<br>- Numeric<br>- ISO 4217<br>**Schema.org property**: Learn more about [Merchant listing (sale pricing) structured data](https://developers.google.com/search/docs/appearance/structured-data/merchant-listing#sale-pricing-example) on Google Search Central. | - Meet the requirements for the price `[price]` attribute.<br>- Submit this attribute (sale price) in addition to the price `[price]` attribute set to the non-sale price.<br>- Accurately submit the product's sale price, and match the sale price with your landing page and the checkout pages.<br>- Do not use the sale price `[sale_price]` attribute to submit loyalty prices (requiring membership in a loyalty program, free or paid) or promotional prices. Instead, use the loyalty program `[loyalty_program]` attribute in supported countries. |
| [Sale price effective date\<br>\<br>`[sale_price_effective_date]`](https://support.google.com/merchants/answer/12471843)<br>The date range during which the sale price applies<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional**<br>**Example**<br>(For UTC+1)<br>`2016-02-24T11:07+0100 /<br>        2016-02-29T23:07+0100`<br>**Syntax**<br>- Max 51 alphanumeric characters<br>- ISO 8601<br>  - `YYYY-MM-DDThh:mm [+hhmm]`<br>  - `YYYY-MM-DDThh:mmZ`<br>- Separate start date and end date with `/`<br>**Schema.org property**: No | - Use together with the sale price `[sale_price]` attribute.<br>- If you don't submit this attribute (sale price effective date), the sale price always applies.<br>- Use a start date before the end date. |
| [Unit pricing measure\<br>\<br>`[unit_pricing_measure]`](https://support.google.com/merchants/answer/12471624)<br>The measure and dimension of your product as it is sold<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional** (except when required by local laws or regulations)<br>**Example**<br>`1.5kg`<br>**Syntax**<br>Numerical value + unit<br>**Supported units**<br>- Weight: `oz`, `lb`, `mg`, `g`, `kg`<br>- Volume US imperial: `floz`, `pt`, `qt`, `gal`<br>- Volume metric: `ml`, `cl`, `l`, `cbm`<br>- Length: `in`, `ft`, `yd`, `cm`, `m`<br>- Area: `sqft`, `sqm`<br>- Per unit: `ct`<br>**Schema.org property**: Yes (Learn more about [Merchant listing (Product, Offer) structured data](https://developers.google.com/search/docs/appearance/structured-data/merchant-listing) on Google Search Central) | - Use the measure or dimension of the product without packaging.<br>- Use a positive number.<br>- For variants:<br>  - Include the same value for item group ID `[item_group_id]` and different values for unit pricing measure. |
| [Unit pricing base measure](https://support.google.com/merchants/answer/6324490)<br>`[unit_pricing_base_measure]`<br>The product’s base measure for pricing (for example, `100ml` means the price is calculated based on a 100ml units)<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional** (except when required by local laws or regulations)<br>**Example**<br>`100g`<br>**Syntax**<br>Integer + unit<br>**Supported integers**<br>`1`, `10`, `100`, `2`, `4`, `8`<br>**Supported units**<br>- Weight: `oz`, `lb`, `mg`, `g`, `kg`<br>- Volume US imperial: `floz`, `pt`, `qt`, `gal`<br>- Volume metric: `ml`, `cl`, `l`, `cbm`<br>- Length: `in`, `ft`, `yd`, `cm`, `m`<br>- Area: `sqft`, `sqm`<br>- Per unit: `ct`<br>**Additional supported metric integer + unit combinations**<br>`75cl`, `750ml`, `50kg`, `1000kg`<br>**Schema.org property**: Yes (Learn more about [Merchant listing (Product, Offer) structured data](https://developers.google.com/search/docs/appearance/structured-data/merchant-listing) on Google Search Central) | - Optional when you submit the unit pricing measure `[unit_pricing_measure]` attribute.<br>- Use the same unit of measure for this attribute (unit pricing measure) and unit pricing base measure.<br>- Keep in mind that the price (or sale price, if active) is used to calculate the unit price of the product. For example, if the price `[price]` attribute is set to `3` `USD`, unit pricing measure is 150ml, and unit pricing base measure is set to 100ml, then the unit price is 2 USD / 100ml. |
| [Installment `[installment]`](https://support.google.com/merchants/answer/12471920)<br>Details of an installment payment plan<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional**<br>**Note**:<br>- Not available for Display Ads.<br>- For Vehicle Ads: only available in certain European countries.<br>- For Shopping Ads and Free listings: Available in Latin America for all product categories and in certain other countries for showing wireless products and services only.<br>**Example** (implies a 199 EUR down payment and a "finance" credit type)<br>`6:30 EUR:199 EUR`<br>**Syntax**<br>This attribute uses 4 sub-attributes:<br>- Months `[months]` (Required)<br>  <br>  <br>   Integer, the number of installments the buyer has to pay.<br>- Amount `[amount]` (Required)<br>  <br>  <br>   ISO 4217, the amount the buyer has to pay per month<br>- Downpayment `[downpayment]` (Optional, not available in Latin America)<br>  <br>  <br>   ISO 4217, the amount the buyer has to pay upfront as a one time payment. Note: if you don't submit the sub-attribute, the default value is 0 or “no down payment”.<br>- Credit type `[credit_type]` (Optional). This sub-attribute uses the following supported values:<br>   <br>  - Finance `[finance]`<br>  - Lease `[lease]`<br>**Note**: if you don't submit the sub-attribute, the default value is finance `[finance]`. This sub-attribute is only applicable for Vehicle Ads.<br>**Schema.org property**: No | - Match the installment option that’s visible on your landing page.<br>- Don't require a loyalty card.<br>- Make sure the price `[price]` attribute is the total price when paid in full up-front and use the installment `[installment]` attribute to indicate an alternative payment option using installments with an optional initial down payment. |
| [Subscription cost `[subscription_cost]`](https://support.google.com/merchants/answer/12472643)<br>Details a monthly or annual payment plan that bundles a communications service contract with a wireless product<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional** (available in certain countries for showing wireless products and services only)<br>**Note**: Not available for Display Ads.<br>**Example**<br>`month:12:35.00USD`<br>**Syntax**<br>- Period `[period]` (Required)<br>  <br>  <br>   The duration of a single subscription period. This sub-attribute uses the following supported values:<br>   <br>  - Month `[month]`<br>  - Year `[year]`<br>- Period length `[period_length]` (Required)<br>  <br>  <br>   Integer, the number of subscription periods (months or years) that the buyer must pay.<br>- Amount `[amount]` (Required)<br>- ISO 4217, the amount the buyer must pay per month. When displaying this amount, Google may round up to the nearest whole unit of local currency to save space. The provided value must still exactly match the amount as shown on your site.<br>**Schema.org property**: No | - Include the total amount due at checkout in the price `[price]` attribute.<br>- When used in combination with the installment `[installment]` attribute, also include the total amount due at checkout in the downpayment `[downpayment]` sub-attribute of the installment `[installment]` attribute.<br>- Match the communications payment plan that you display on your landing page. The plan must be easy to find on the landing page. |
| [Loyalty program `[loyalty_program]`](https://support.google.com/merchants/answer/12922446)<br>The loyalty program `[loyalty_program]` attribute allows setting up of member prices, loyalty points, and loyalty shipping.<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional** (available for Australia, Japan, United Kingdom, and the United States only)<br>**Example**<br>`my_loyalty_program:silver:10 USD::10:`<br>**Syntax**<br>This attribute uses 7 sub-attributes:<br>- Program label `[program_label]` (Required)<br>  <br>  <br>   The loyalty program label set in your loyalty program settings in Merchant Center.<br>- Tier label `[tier_label]` (Required)<br>  <br>  <br>   The tier label set in your program settings in Merchant Center, used to differentiate benefits between each tier.<br>- Price `[price]` (Optional) The member specific price for the program and tier. This will display alongside the non-member price to give shoppers an idea of the benefits of joining your program. This attribute should be used for free and paid memberships.<br>- Loyalty points `[loyalty_points]` (Optional) The points that the members gain on purchasing the product on your website. This needs to be a whole number.<br>- Member price effective date `[member_price_effective_date]` (Optional): This sub-attribute allows merchants to specify when their member pricing benefit begins and ends.<br>- Shipping label `[shipping_label] `(Optional): This sub-attribute allows merchants to specify which offers are eligible for loyalty shipping. Choose your own definition for this value.<br>**Schema.org property**: Yes (Learn more about [Merchant listing (Member prices) structured data](https://developers.google.com/search/docs/appearance/structured-data/merchant-listing#member-price-example) on Google Search Central) | - Submit the loyalty program `[loyalty_program]` attribute to match the loyalty program label and tiers configured under your Merchant Center account.<br>- Make sure that member prices are clearly displayed on your website either on your landing page (as a $ value or percentage off), in your loyalty overview page, or a dedicated sales event page.<br>- Ensure that member prices match between your product data source, landing page, and checkout.<br>- Member price for free and paid tiers needs to be submitted via this attribute. Submitting member price using price `[price]` or sale price `[sale_price]` is not allowed. |
| [Minimum price `[auto_pricing_min_price]`](https://support.google.com/merchants/answer/********)<br>The lowest price to which a product's price can be reduced. Google uses this information for features such as sale price suggestions, automated discounts or dynamic promotions.<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional**<br>**Example**<br>`15.00 USD`<br>**Syntax**<br>- Numeric<br>- ISO 4217<br>**Schema.org property**: No | - Submit a minimum price `[auto_pricing_min_price]` attribute.<br>   <br>  - If you are using the automated discounts or dynamic promotions feature, to specify the minimum price to which your product can be reduced.<br>  - If you want to limit sale price suggestions to a minimum price, for example, to comply with local pricing laws or to indicate a MAP (minimum advertised price). |

## Product category

You can use these attributes to organize your advertising campaigns in Google Ads and to override Google’s automatic product categorization in specific cases.

| Attribute and format | Minimum requirements at a glance |
| --- | --- |
| [Google product category `[google_product_category]`](https://support.google.com/merchants/answer/6324436)<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional**<br>Google-defined product category for your product<br>**Example**<br>`Apparel & Accessories > Clothing > Outerwear > Coats & Jackets`<br>or<br>`371`<br>**Syntax**<br>Value from the Google product taxonomy<br>- The numerical category ID, or<br>- The full path of the category<br>**Supported values**<br>[Google product taxonomy](https://support.google.com/merchants/answer/12472026)<br>**Schema.org property**: No | - Include only one category.<br>- Include the most relevant category.<br>- Include either the full path of the category or the numerical category ID, but not both. It is recommended to use the category ID.<br>- Include a specific category for certain products.<br>  - Alcoholic beverages must be submitted to only certain categories.<br>- Mobile devices sold with contract must be submitted as either:<br>  - `Electronics > Communications > Telephony > Mobile Phones` (ID: `267`<br>    <br>  - For tablets: `Electronics > Computers > Tablet Computers` (ID: `4745`)<br>- Gift Cards must be submitted as `Arts & Entertainment > Party & Celebration > Gift Giving > Gift Cards & Certificates` (ID: `53`) |
| [Product type `[product_type]`](https://support.google.com/merchants/answer/6324406)<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional**<br>Product category that you define for your product<br>**Example**<br>`Home > Women > Dresses > Maxi Dresses`<br>**Syntax**<br>Max 750 alphanumeric character<br>**Schema.org property**: No | - Include the full category. For example, include `Home > Women > Dresses > Maxi Dresses` instead of just Dresses<br>- Only the first product type value will be used to organize bidding and reporting in Google Ads Shopping campaigns |

## Product identifiers

These attributes are used to provide product identifiers that define the products you're selling in the global marketplace and can help boost the performance of your ads and free listings.

| Attribute and format | Minimum requirements at a glance |
| --- | --- |
| [Brand `[brand]`](https://support.google.com/merchants/answer/12468352)<br>Your product’s brand name<br>![Required](https://storage.googleapis.com/support-kms-prod/ncoEh9hPBwKEuXOUKRffUwCyLKZVt7PeCt6w)**Required** (For all new products, except movies, books, and musical recording brands)<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional** for all other products<br>**Example**<br>`Google`<br>**Syntax**<br>Max 70 characters<br>**Schema.org property:** Yes (Learn more about [Merchant listing (Product, Offer) structured data](https://developers.google.com/search/docs/appearance/structured-data/merchant-listing) on Google Search Central) | - Provide the brand name of the product generally recognized by consumers.<br>- Providing the correct brand for a product will ensure the best user experience and result in the best performance.<br>- Only provide **your own brand** name as the brand if you manufacture the product or if your product falls into a generic brand category.<br>   <br>  - For example, you could submit **your** **own brand** name as the brand if you sell private-label products or customized jewelry.<br>- For products that truly do not have a brand (for example, a vintage dress without a label, generic electronics accessories, etc.) leave this field empty.<br>- Don't submit values such as "N/A", "Generic", "No brand", or "Does not exist".<br>- For compatible products:<br>  - Submit the GTIN and brand from the manufacturer who actually built the compatible product.<br>  - Don't provide the Original Equipment Manufacturer (OEM) brand to indicate that your product is compatible with or a replica of the OEM brand's product. |
| [GTIN `[gtin]`](https://support.google.com/merchants/answer/12473440)<br>Your product’s Global Trade Item Number (GTIN)<br>![Required](https://storage.googleapis.com/support-kms-prod/ncoEh9hPBwKEuXOUKRffUwCyLKZVt7PeCt6w)**Required** (For all products with a known [GTIN](https://support.google.com/merchants/answer/12473440) to enable full offer performance)<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional** (strongly recommended) for all other products<br>**Example**<br>`3234567890126`<br>**Syntax**<br>Max 50 numeric characters (max 14 per value - added spaces and dashes are ignored)<br>**Supported values**<br>- **UPC (in North America / GTIN-12)**<br>  <br>  <br>   12-digit number like 323456789012<br>  <br>  <br>   8-digit UPC-E codes should be converted to 12-digit codes<br>- **EAN (in Europe / GTIN-13)**<br>  <br>  <br>   13-digit number like 3001234567892<br>- **JAN (in Japan / GTIN-13)**<br>  <br>  <br>   8 or 13-digit number like 49123456 or 4901234567894<br>- **ISBN (for books)**<br>  <br>  <br>   10 or 13-digit number like 1455582344 or 978-1455582341. If you have both, only include the 13-digit number. ISBN-10 are deprecated and should be converted to ISBN-13<br>- **ITF-14 (for multipacks / GTIN-14)**<br>  <br>  <br>   14-digit number like 10856435001702<br>**Schema.org property:**  `` Yes (Learn more about [Merchant listing (Product, Offer) structured data](https://developers.google.com/search/docs/appearance/structured-data/merchant-listing) on Google Search Central) | - Exclude dashes and spaces.<br>- Submit only valid GTINs as defined in the official GS1 validation guide, which includes these requirements:<br>  - The checksum digit is present and correct<br>  - The GTIN is not restricted (GS1 prefix ranges 02, 04, 2)<br>  - The GTIN is not a coupon (GS1 prefix ranges 98 - 99)<br>- Providing the correct GTIN for a product will ensure the best user experience and result in the best performance.<br>- Only provide a GTIN if you’re sure it is correct. When in doubt don’t provide this attribute (for example, do not guess or make up a value). If you submit a product with an incorrect GTIN value, your product will be disapproved.<br>- For compatible products:<br>  - Submit the GTIN and brand from the manufacturer who actually built the compatible product.<br>  - Don't provide the Original Equipment Manufacturer (OEM) brand to indicate that your product is compatible with or a replica of the OEM brand's product.<br>- For multipacks:<br>  - Use the product identifiers that relates to the multipack.<br>- For bundles:<br>  - Use the product identifiers for the main product in the bundle.<br>- If you offer customization, engraving, or other personalization of a product that's been assigned a GTIN by the manufacturer:<br>  - Submit the GTIN and use the [bundle `[is_bundle]`](https://support.google.com/merchants/answer/12472645) attribute to let Google know that the product includes customization. |
| [MPN `[mpn]`](https://support.google.com/merchants/answer/12474954)<br>Your product’s Manufacturer Part Number (MPN)<br>![Required](https://storage.googleapis.com/support-kms-prod/ncoEh9hPBwKEuXOUKRffUwCyLKZVt7PeCt6w)**Required** (Only if your product does not have a manufacturer assigned GTIN)<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional** for all other products<br>**Example**<br>`GO12345OOGLE`<br>**Syntax**<br>Max 70 alphanumeric characters<br>**Schema.org property:**  `` Yes (Learn more about [Merchant listing (Product, Offer) structured data](https://developers.google.com/search/docs/appearance/structured-data/merchant-listing) on Google Search Central) | - Only submit MPNs assigned by a manufacturer.<br>- Use the most specific MPN possible.<br>  - For example, different colors of a product should have different MPNs.<br>- Providing the correct MPN for a product (when required) will ensure the best user experience and result in the best performance.<br>- Only provide an MPN if you’re sure it’s correct. When in doubt don’t provide this attribute (for example, don’t guess or make up a value).<br>- If you submit a product with an incorrect MPN value, your product will be disapproved. |
| [Identifier exists `[identifier_exists]`](https://support.google.com/merchants/answer/12472746)<br>Use to indicate whether or not the unique product identifiers (UPIs) GTIN, MPN, and brand are available for your product.<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional**<br>**Example**<br>`no`<br>**Supported values**<br>- Yes `[yes]`<br>  <br>  <br>   Product identifiers are assigned to the new product by the manufacturer<br>- No `[no]`<br>  <br>  <br>   Product lacks a brand, GTIN, or MPN (see requirements to the right). If set to `no`, still provide the UPIs you have.<br>**Schema.org property**: No | - If you don't submit the attribute, the default value is `yes`.<br>- Your product’s category type determines which unique product identifiers (GTIN, MPN, brand) are required.<br>- Submit the [identifier exists](https://support.google.com/merchants/answer/12472746) attribute and set the value to `no` if:<br>   <br>  - Your product is a media item and the GTIN is unavailable ( **Note:** ISBN and SBN codes are accepted as GTINs<br>  - Your product is an apparel (clothing) item and the brand is unavailable<br>  - In all other categories, your product doesn’t have a GTIN, or a combination of MPN and brand<br>- If a product does have unique product identifiers, don’t submit this attribute with a value of “ `no`” or the product may be disapproved. |

## Detailed product description

These attributes are used to provide product identifiers that define the products you're selling in the global marketplace and can help boost the performance of your ads and free listings.

| Attribute and format | Minimum requirements at a glance |
| --- | --- |
| [Condition `[condition]`](https://support.google.com/merchants/answer/12471921)<br>The condition of your product at time of sale<br>![Required](https://storage.googleapis.com/support-kms-prod/ncoEh9hPBwKEuXOUKRffUwCyLKZVt7PeCt6w)**Required** if your product is used or refurbished<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional** for new products<br>**Example**<br>`new`<br>**Supported values**<br>- New `[new]`<br>  <br>  <br>   Brand new, original, unopened packaging<br>- Refurbished `[refurbished]`<br>  <br>  <br>   Professionally restored to working order, comes with a warranty, may or may not have the original packaging<br>- Used `[used]`<br>  <br>  <br>   Previously used, original packaging opened or missing<br>**Schema.org property:** Yes (Learn more about [Merchant listing (Product, Offer) structured data](https://developers.google.com/search/docs/appearance/structured-data/merchant-listing) on Google Search Central) |  |
| [Adult `[adult]`](https://support.google.com/merchants/answer/12471844)<br>Indicate a product includes sexually suggestive content<br>![Required](https://storage.googleapis.com/support-kms-prod/ncoEh9hPBwKEuXOUKRffUwCyLKZVt7PeCt6w)**Required**(If a product contains adult content)<br>**Example**<br>`yes`<br>**Supported values**<br>- Yes `[yes]`<br>- No `[no]`<br>**Schema.org property**: No | - Set the value of this attribute to `yes` if this individual product contains nudity or sexually suggestive content. If you don't submit the attribute, the default value is `no`. [Learn about the adult-oriented content policy](https://support.google.com/merchants/answer/6150138)<br>- If your website is generally focused on an adult audience and contains adult-oriented content with or without nudity, indicate that in your Merchant Center settings.<br>- If you use **Merchant Center Next**, find these settings in the Business details tab.<br>- If you use the **classic Merchant Center**, find these settings under “Tools & Settings” and then select “Account”. |
| [Multipack `[multipack]`](https://support.google.com/merchants/answer/********)<br>The number of identical products sold within a merchant-defined multipack<br>![Required](https://storage.googleapis.com/support-kms-prod/ncoEh9hPBwKEuXOUKRffUwCyLKZVt7PeCt6w)**Required** (For multipack products in Australia, Brazil, Czechia, France, Germany, Italy, Japan, Netherlands, Spain, Switzerland, the UK and the US)<br>![Required](https://storage.googleapis.com/support-kms-prod/ncoEh9hPBwKEuXOUKRffUwCyLKZVt7PeCt6w)**Required** for free listings on Google if you’ve created a multipack<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional** for all other products and target countries<br>**Example**<br>6<br>**Syntax**<br>Integer<br>**Schema.org property**: No | - Submit this attribute if you defined a custom group of identical products and are selling them as a single unit of sale (for example, you're selling 6 bars of soap together).<br>- Submit the number of products in your multipack. If you don't submit the attribute, the default value is `0`.<br>- If the product's manufacturer assembled the multipack instead of you, don't submit this attribute. |
| [Bundle `[is_bundle]`](https://support.google.com/merchants/answer/12472645)<br>Indicates a product is a merchant-defined custom group of different products featuring one main product<br>![Required](https://storage.googleapis.com/support-kms-prod/ncoEh9hPBwKEuXOUKRffUwCyLKZVt7PeCt6w)**Required** (For bundles in Australia, Brazil, Czechia, France, Germany, Italy, Japan, Netherlands, Spain, Switzerland, the UK and the US)<br>![Required](https://storage.googleapis.com/support-kms-prod/ncoEh9hPBwKEuXOUKRffUwCyLKZVt7PeCt6w)**Required** for free listings on Google if you’ve created a bundle containing a main product<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional** for all other products and target countries<br>**Example**<br>`yes`<br>**Supported values**<br>- Yes `[yes]`<br>- No `[no]`<br>**Schema.org property**: No | - Submit `yes` if you're selling a custom bundle of different products that you created, and the bundle includes a main product (for example, a camera combined with a lens and bag). If you don't submit the attribute, the default value is `no`.<br>- Don't use this attribute for bundles without a clear main product (for example, a gift basket containing cheese and crackers). |
| [Certification `[certification]`](https://support.google.com/merchants/answer/13528839)<br>Certifications, such as energy efficiency ratings, associated with your product<br>Available for the EU and EFTA countries and the UK<br>![Required](https://storage.googleapis.com/support-kms-prod/ncoEh9hPBwKEuXOUKRffUwCyLKZVt7PeCt6w)**Required** for products that require certain certification information to be shown in your Shopping ads or free listings, for example due to local energy efficiency labeling regulations<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional** for all other products<br>**Note**: If you aren't able to locate your product in the [EU EPREL](https://commission.europa.eu/energy-climate-change-environment/standards-tools-and-labels/products-labelling-rules-and-requirements/energy-label-and-ecodesign/product-database) database, you can use the energy efficiency class `[energy_efficiency_class]`, minimum energy efficiency class `[min_energy_efficiency_class]`, and maximum energy efficiency class `[max_energy_efficiency_class]` attributes instead for a limited transition period.<br>**Example**<br>`EC:EPREL:123456`<br>**Syntax**<br>This attribute uses the following sub-attributes:<br>- Authority `[certification_` `authority]` Certification authority. Only "EC" or "European\_Commission" supported.<br>- Name `[certification_` `name]` Name of the certification. Only "EPREL" supported.<br>- Code `[certification_` `code]` Code of the certification. For example, for the EPREL certificate with the link https://eprel.ec.europa.eu/screen/product<br>  <br>  <br>   /dishwashers2019/123456 the code is 123456<br>**Schema.org property**: No | Consult EU energy efficiency regulations or any applicable local law to determine if you need to provide this attribute. This includes products covered by [EU energy labels](https://commission.europa.eu/energy-climate-change-environment/standards-tools-and-labels/products-labelling-rules-and-requirements/energy-label-and-ecodesign/about_en#Energylabels), for example:<br>- Fridges and freezers<br>- Dishwashers<br>- Televisions and other external monitors<br>- Household washing machines and washer-dryers<br>- Refrigerating appliances with a direct-sales function<br>- Light sources |
| [Energy efficiency class `[energy_efficiency_class]`](https://support.google.com/merchants/answer/12472144)<br>Your product’s energy label<br>Available for the EU and EFTA countries and the UK<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional** (except when required by local law or regulations)<br>**Note**: This attribute is being deprecated. Please use the [certification `[certification]`](https://support.google.com/merchants/answer/13528839) attribute instead to show the EU energy efficiency class.<br>**Example**<br>`A+`<br>**Supported values**<br>- `A+++`<br>- `A++`<br>- `A+`<br>- `A`<br>- `B`<br>- `C`<br>- `D`<br>- `E`<br>- `F`<br>- `G`<br>**Schema.org property:** Yes (Learn more about [Merchant listing (Product, Offer) structured data](https://developers.google.com/search/docs/appearance/structured-data/merchant-listing) on Google Search Central) | - Include the legally required energy label.<br>- To be used in combination with minimum energy efficiency class `[min_energy_efficiency_class]` and maximum energy efficiency class `[max_energy_efficiency_class]` to create an energy efficiency label, for example, A+ (A+++ to G). |
| [Minimum energy efficiency class `[min_energy_efficiency_class]`](https://support.google.com/merchants/answer/12472144)<br>Your product’s energy label<br>Available for the EU and EFTA countries and the UK<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional** (except when required by local laws or regulations)<br>**Note**: This attribute is being deprecated. Please use the [certification `[certification]`](https://support.google.com/merchants/answer/13528839) attribute instead to show the EU energy efficiency class.<br>Available for EU & CH only<br>**Example**<br>`A+++`<br>**Supported values**<br>- `A+++`<br>- `A++`<br>- `A`<br>- `B`<br>- `C`<br>- `D`<br>- `E`<br>- `F`<br>- `G`<br>**Schema.org property:** Yes (Learn more about [Merchant listing (Product, Offer) structured data](https://developers.google.com/search/docs/appearance/structured-data/merchant-listing) on Google Search Central) | - Include the legally required energy label.<br>- To be used in combination with energy efficiency class `[energy_efficiency_class]` and maximum energy efficiency class `[max_energy_efficiency_class]` to create an energy efficiency label, for example, A+ (A+++ to D). |
| [Maximum energy efficiency class `[max_energy_efficiency]`](https://support.google.com/merchants/answer/12472144)<br>Your product’s energy label<br>Available for the EU and EFTA countries and the UK<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional** (except when required by local laws or regulations)<br>**Note**: This attribute is being deprecated. Please use the [certification `[certification]`](https://support.google.com/merchants/answer/13528839) attribute instead to show the EU energy efficiency class.<br>Available for EU & CH only<br>**Example**<br>D<br>**Supported values**<br>- `A+++`<br>- `A++`<br>- `A`<br>- `B`<br>- `C`<br>- `D`<br>- `E`<br>- `F`<br>- `G`<br>**Schema.org property:** Yes (Learn more about [Merchant listing (Product, Offer) structured data](https://developers.google.com/search/docs/appearance/structured-data/merchant-listing) on Google Search Central) | - Include the legally required energy label<br>- To be used in combination with energy efficiency class `[energy_efficiency_class]` and minimum energy efficiency class `[min_energy_efficiency_class]` to create a textual or graphical energy efficiency label, for example, A+ (G to A+++) |
| [Age group `[age_group]`](https://support.google.com/merchants/answer/12472028)<br>The demographic for which your product is intended<br>![Required](https://storage.googleapis.com/support-kms-prod/ncoEh9hPBwKEuXOUKRffUwCyLKZVt7PeCt6w)**Required** (For all apparel products that are targeted to people in Brazil, France, Germany, Japan, the UK, and the US as well as all products with assigned age groups)<br>![Required](https://storage.googleapis.com/support-kms-prod/ncoEh9hPBwKEuXOUKRffUwCyLKZVt7PeCt6w)**Required** for free listings for all `Apparel & Accessories` (ID: `166`) products<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional** for all other products and target countries<br>**Example**<br>`infant`<br>**Supported values**<br>- Newborn `[newborn]`<br>  <br>  <br>   0-3 months old<br>- Infant `[infant]`<br>  <br>  <br>   3-12 months old<br>- Toddler `[toddler]`<br>  <br>  <br>   1-5 years old<br>- Kids `[kids]`<br>  <br>  <br>   5-13 years old<br>- Adult `[adult]`<br>  <br>  <br>   Teens or older<br>**Schema.org property:** Yes (Learn more about [Merchant listing (Product, Offer) structured data](https://developers.google.com/search/docs/appearance/structured-data/merchant-listing) on Google Search Central) | - Include one value per product.<br>- For variants:<br>  - Include the same value for item group ID `[item_group_id]` and different values for age group. |
| [Color `[color]`](https://support.google.com/merchants/answer/12471922)<br>Your product’s color(s)<br>![Required](https://storage.googleapis.com/support-kms-prod/ncoEh9hPBwKEuXOUKRffUwCyLKZVt7PeCt6w)**Required** (For all apparel products that are targeted to Brazil, France, Germany, Japan, the UK, and the US as well as all products available in different colors)<br>![Required](https://storage.googleapis.com/support-kms-prod/ncoEh9hPBwKEuXOUKRffUwCyLKZVt7PeCt6w)**Required** for free listings for all `Apparel & Accessories` (ID: `166`) products<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional** for all other products and target countries<br>**Example**<br>Black<br>**Syntax**<br>Max 100 alphanumeric characters (max 40 characters per color)<br>**Schema.org property:**  `` Yes (Learn more about [Merchant listing (Product, Offer) structured data](https://developers.google.com/search/docs/appearance/structured-data/merchant-listing) on Google Search Central) | - Don’t use a number such as "0", "2", or "4".<br>- Don’t use characters that aren’t alphanumeric such as "#fff000".<br>- Don’t use only one letter such as R. (For Chinese, Japanese, or Korean languages, you can include a single character such as 红.)<br>- Don’t reference the product or image such as “see image”.<br>- Don't combine several color names into one word, such as "RedPinkBlue" _._ Instead, separate them with a `/`, such as "Red/Pink/Blue" _._ Don’t use a value that isn’t a color, such as "multicolor", "various", "variety", "men's", "women's", or "N/A".<br>- If your product features multiple colors, list the primary color first.<br>- For variants:<br>  - Include the same value for item group ID `[item_group_id]` and different values for color `[color]` |
| [Gender `[gender]`](https://support.google.com/merchants/answer/12471626)<br>The gender for which your product is intended<br>![Required](https://storage.googleapis.com/support-kms-prod/ncoEh9hPBwKEuXOUKRffUwCyLKZVt7PeCt6w)**Required** (Required for all apparel items that are targeted to people in Brazil, France, Germany, Japan, the UK, and the US as well as all gender-specific products)<br>![Required](https://storage.googleapis.com/support-kms-prod/ncoEh9hPBwKEuXOUKRffUwCyLKZVt7PeCt6w)**Required** for free listings for all Google `Apparel & Accessories` (ID: `166`) products<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional** for all other products and target countries<br>**Example**<br>`Unisex`<br>**Supported values**<br>- Male `[male]`<br>- Female `[female]`<br>- Unisex `[unisex]`<br>**Schema.org property:**  `` Yes (Learn more about [Merchant listing (Product, Offer) structured data](https://developers.google.com/search/docs/appearance/structured-data/merchant-listing) on Google Search Central) | - For some `Apparel & Accessories` (ID: `166`) categories like `Shoelaces` (ID: `1856`), this attribute is recommended instead of required since these categories aren't dependent on gender.<br>- For variants:<br>  - Include the same value for item group ID `[item_group_id] ` and different values for gender |
| [Material `[material]`](https://support.google.com/merchants/answer/12472145)<br>Your product’s fabric or material<br>![Required](https://storage.googleapis.com/support-kms-prod/ncoEh9hPBwKEuXOUKRffUwCyLKZVt7PeCt6w)**Required** (if relevant for distinguishing different products in a set of variants)<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional** for all other products<br>**Example**<br>leather<br>**Syntax**<br>Max 200 characters<br>**Schema.org property:**  `` Yes (Learn more about [Merchant listing (Product, Offer) structured data](https://developers.google.com/search/docs/appearance/structured-data/merchant-listing) on Google Search Central)<br>`` | - To indicate multiple materials for a single product (not variants), add a primary material, followed by up to 2 secondary materials, separated by a `/`.<br>   <br>  - For example, instead of "CottonPolyesterElastane", use "cotton/polyester/elastane".<br>- For variants:<br>  - Include the same value for the item group ID `[item_group_id]` attribute and different values for the material attribute |
| [Pattern `[pattern]`](https://support.google.com/merchants/answer/12472146)<br>Your product’s pattern or graphic print<br>![Required](https://storage.googleapis.com/support-kms-prod/ncoEh9hPBwKEuXOUKRffUwCyLKZVt7PeCt6w)**Required** (if relevant for distinguishing different products in a set of variants)<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional** for all other products<br>**Example**<br>striped<br>polka dot<br>paisley<br>**Syntax**<br>Max 100 characters<br>**Schema.org property:**  `` Yes (Learn more about [Merchant listing (Product, Offer) structured data](https://developers.google.com/search/docs/appearance/structured-data/merchant-listing) on Google Search Central)<br>`` | - For variants:<br>  - Include the same value for the item group ID `[item_group_id]` attribute and different values for the pattern attribute |
| [Size `[size]`](https://support.google.com/merchants/answer/12471627)<br>Your product’s size<br>![Required](https://storage.googleapis.com/support-kms-prod/ncoEh9hPBwKEuXOUKRffUwCyLKZVt7PeCt6w)**Required** (Required for all apparel products in`Apparel & Accessories > Clothing`(ID: `1604`) and `Apparel & Accessories > Shoes`(ID:`187`) categories targeted to people in Brazil, France, Germany, Japan, the UK, and the US as well as all products available in different sizes)<br>![Required](https://storage.googleapis.com/support-kms-prod/ncoEh9hPBwKEuXOUKRffUwCyLKZVt7PeCt6w)**Required** for free listings for all `Apparel & Accessories > Clothing`(ID:`1604`) and `Apparel & Accessories > Shoes` (ID:`187`) products<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional** for all other products and target countries<br>**Example**<br>XL<br>**Syntax**<br>Max 100 characters<br>**Schema.org property:**  `` Yes (Learn more about [Merchant listing (Product, Offer) structured data](https://developers.google.com/search/docs/appearance/structured-data/merchant-listing) on Google Search Central) | - For variants:<br>  - Include this with the same value for item group ID `[item_group_id]` and different values for size `[size]`<br>- If sizes contain multiple dimensions, condense them into one value. For example, "16/34 Tall" is for neck size of 16 inches, sleeve length of 34 inches, and “Tall” fit<br>- If your item is one size fits all or one size fits most, you can use `one_size`, `OS`, `one_size fits_all`, `OSFA`, `one_size_fits_most`, or `OSFM`.<br>- For merchant-defined multipack products, submit the multipack quantity using the multipack `[multipack]` attribute. Do not submit the multipack quantity under the `size` attribute. |
| [Size type `[size_type]`](https://support.google.com/merchants/answer/12471628)<br>Your apparel product’s cut<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional** (Available for apparel products only)<br>**Example**<br>`maternity`<br>**Supported values**<br>- Regular `[regular]`<br>- Petite `[petite]`<br>- Maternity `[maternity]`<br>- Big `[big]`<br>- Tall `[tall]`<br>- Plus `[plus]`<br>**Schema.org property:** Yes (Learn more about [Merchant listing (Product, Offer) structured data](https://developers.google.com/search/docs/appearance/structured-data/merchant-listing) on Google Search Central) | - Submit up to 2 values.<br>- If you don't submit the attribute, the default value is `regular`. |
| [Size system `[size_system]`](https://support.google.com/merchants/answer/12472828)<br>The country of the size system used by your product<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional** (Available for apparel products only)<br>**Example**<br>`US`<br>**Supported values**<br>- `US`<br>- `UK`<br>- `EU`<br>- `DE`<br>- `FR`<br>- `JP`<br>- `CN`<br>- `IT`<br>- `BR`<br>- `MEX`<br>- `AU`<br>**Schema.org property:** Yes (Learn more about [Merchant listing (Product, Offer) structured data](https://developers.google.com/search/docs/appearance/structured-data/merchant-listing) on Google Search Central) | - If you don't submit the attribute, the default value is your **target country**. |
| [Item group ID `[item_group_id]`](https://support.google.com/merchants/answer/12472646)<br>ID for a group of products that come in different versions (variants)<br>![Required](https://storage.googleapis.com/support-kms-prod/ncoEh9hPBwKEuXOUKRffUwCyLKZVt7PeCt6w)**Required** (Brazil, France, Germany, Japan, the United Kingdom, and the US if the product is a variant)<br>![Required](https://storage.googleapis.com/support-kms-prod/ncoEh9hPBwKEuXOUKRffUwCyLKZVt7PeCt6w)**Required** for free listings for all product variants<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional** for all other products and target countries<br>**Example**<br>AB12345<br>**Syntax**<br>Max 50 alphanumeric characters<br>**Schema.org property:** Yes (Learn more about [Merchant listing (Product, Offer) structured data](https://developers.google.com/search/docs/appearance/structured-data/merchant-listing) on Google Search Central) | - Use a unique value for each group of variants. Use the parent SKU where possible.<br>- Keep the value the same when updating your product data.<br>- Use only valid unicode characters.<br>- Use an item group ID for a set of products that differ by one or more of these attributes:<br>  - [Color `[color]`](https://support.google.com/merchants/answer/12471922)<br>  - [Size `[size]`](https://support.google.com/merchants/answer/12471627)<br>  - [Pattern `[pattern]`](https://support.google.com/merchants/answer/12472146)<br>  - [Material `[material]`](https://support.google.com/merchants/answer/12472145)<br>  - [Age group `[age_group]`](https://support.google.com/merchants/answer/12472028)<br>  - [Gender `[gender]`](https://support.google.com/merchants/answer/12471626)<br>- Include the same attributes for each product in the item group. For example, if a product varies by size and color, submit [size `[size]`](https://support.google.com/merchants/answer/12471627) and [color `[color]`](https://support.google.com/merchants/answer/12471922) for every product that share the same value for [item group ID `[item_group_id]`](https://support.google.com/merchants/answer/12472646).<br>- If your products differ by design elements that aren't represented by the attributes above, don't use item group ID. |
| [Product length `[product_length]`](https://support.google.com/merchants/answer/12472549)<br>Your product's length<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional**<br>**Example**<br>`20 in`<br>**Syntax**<br>Number + unit<br>**Supported values**<br>`1-3000`<br>- Decimal values are supported<br>**Supported units**<br>- `cm`<br>- `in`<br>**Schema.org property:** Yes (Learn more about [Merchant listing (Product, Offer) structured data](https://developers.google.com/search/docs/appearance/structured-data/merchant-listing) on Google Search Central) | - Include as many of the product measurement attributes as possible.<br>- Use the same unit of measurement for each product dimension attribute (including product length, width, and height). Otherwise, the information won't be displayed. |
| [Product width `[product_width]`](https://support.google.com/merchants/answer/12472549)<br>Your product's width<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional**<br>**Example**<br>`20 in`<br>**Syntax**<br>Number + unit<br>**Supported values**<br>`1-3000`<br>- Decimal values are supported<br>**Supported units**<br>- `cm`<br>- `in`<br>**Schema.org property:** Yes (Learn more about [Merchant listing (Product, Offer) structured data](https://developers.google.com/search/docs/appearance/structured-data/merchant-listing) on Google Search Central) | - Include as many of the product measurement attributes as possible.<br>- Use the same unit of measurement for each product dimension attribute (including product lengths, width, and height). Otherwise, the information won't be displayed. |
| [Product height `[product_height]`](https://support.google.com/merchants/answer/12472549)<br>Your product's height<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional**<br>**Example**<br>`20 in`<br>**Syntax**<br>Number + unit<br>**Supported values**<br>`1-3000`<br>- Decimal values are supported<br>**Supported units**<br>- `cm`<br>- `in`<br>**Schema.org property:** Yes (Learn more about [Merchant listing (Product, Offer) structured data](https://developers.google.com/search/docs/appearance/structured-data/merchant-listing) on Google Search Central) | - Include as many of the product measurement attributes as possible.<br>- Use the same unit of measurement for each product dimension attribute (including product lengths, width, and height). Otherwise, the information won't be displayed. |
| [Product weight `[product_weight]`](https://support.google.com/merchants/answer/12472549)<br>Your product's weight<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional**<br>**Example**<br>`3.5 lb`<br>**Syntax**<br>Number + unit<br>**Supported values**<br>`0-2000`<br>- Decimal values are supported<br>**Supported units**<br>- lb<br>- oz<br>- g<br>- kg<br>**Schema.org property:** Yes (Learn more about [Merchant listing (Product, Offer) structured data](https://developers.google.com/search/docs/appearance/structured-data/merchant-listing) on Google Search Central) | - Use the actual assembled product weight for this attribute.<br>- If your product comes in multiple pieces, for example, as part of a bundle, use the complete weight of all the pieces in the listing. |
| [Product detail `[product_detail]`](https://support.google.com/merchants/answer/9218260)<br>Technical specifications or additional details of your product<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional**<br>**Example**<br>General:Product Type:Digital player<br>**Syntax**<br>This attribute uses 3 sub-attributes:<br>- Section name ` [section_name]`: Max 140 characters<br>- Attribute name ` [attribute_name]`: Max 140 characters<br>- Attribute value ` [attribute_value]`: Max 1000 characters<br>**Schema.org property**: No | - Don't add information covered in other attributes, all capital letters, gimmicky foreign characters, promotion text, or list keywords or search terms.<br>- Don’t add information such as price, sale price, sale dates, shipping, delivery date, other time-related information, or your company’s name.<br>- Only provide an attribute name and value when the value is confirmed. For example, provide “Vegetarian=False” if a food product is not vegetarian. |
| [Product highlight `[product_highlight]`](https://support.google.com/merchants/answer/12471629)<br>The most relevant highlights of your products<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional**<br>**Example**<br>Supports thousands of apps, including Netflix, YouTube, and HBO Max<br>**Syntax**<br>Max 150 characters<br>**Schema.org property**: No | - Use between 2 and 100 product highlights.<br>- Describe only the product itself.<br>- Don't list keywords or search terms.<br>- Don’t include promotional text, all capital letters, or gimmicky foreign characters. |

## Shopping campaigns and other configurations

These attributes are used to control how your product data is used when you create advertising campaigns in Google Ads.

| Attribute and format | Minimum requirements at a glance |
| --- | --- |
| [Ads redirect `[ads_redirect]`](https://support.google.com/merchants/answer/12471846)<br>A URL used to specify additional parameters for your product page. Customers will be sent to this URL rather than the value that you submit for the link `[link]` or mobile link `[mobile_link]` attributes<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional**<br>**Example**<br>`http://www.example.com/product.html`<br>**Syntax**<br>Max 2000 characters<br>**Schema.org property**: No | - Submit the same registered domain as for the link `[link]` attribute (and the mobile link `[mobile_link]` attribute, if present).<br>- Valid registered domains include "example.com", "m-example.com", "example.co.uk", "example.com.ai", and "bar.tokyo.jp".<br>- URLs submitted with invalid domains, such as "example.zz" or "example.comic", will not be accepted. For more details on valid registered domains, see ads redirect. |
| [Custom label 0-4 `[custom_label_0-4]`](https://support.google.com/merchants/answer/12472830)<br>Label that you assign to a product to help organize bidding and reporting in Shopping campaigns<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional**<br>**Example**<br>Seasonal<br>Clearance<br>Holiday<br>Sale<br>Price range<br>**Syntax**<br>Max 100 characters<br>**Schema.org property**: No | - Use a value that you'll recognize in your Shopping campaign. The value won't be shown to customers who see your ads and free listings.<br>- Submit up to 5 custom labels per product by including this attribute multiple times:<br>  - `custom_label_0`<br>  - `custom_label_1`<br>  - `custom_label_2`<br>  - `custom_label_3`<br>  - `custom_label_4`<br>- Use only 1,000 unique values for each custom label across your Merchant Center account. |
| [Promotion ID `[promotion_id]`](https://support.google.com/merchants/answer/********)<br>An identifier that allows you to match products to promotions<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional** (Required for promotions in Australia, France, Germany, India, the UK and the US)<br>**Example**<br>ABC123<br>**Syntax**<br>Max 50 characters<br>**Schema.org property**: No | - Use a unique and case sensitive ID without spaces or symbols (for example, %, !).<br>- To map specific promotions to specific products, submit the same promotion ID in your product data and promotion data.<br>- Submit up to 10 promotion IDs for one product by including this attribute multiple times. |
| [Lifestyle image link](https://support.google.com/merchants/answer/9103186) `[lifestyle_image_link]`<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional**<br>Attribute used to include the URL for a lifestyle image for your product<br>Only available for browsy surfaces<br>**Example**<br>https://www.example.com/image1.jpg<br>**Syntax**<br>Max 2000 characters<br>**Schema.org property**: No | - Use a URL that points to an image in a supported file format<br>- Start with `http` or `https` and comply with RFC 3986<br>- Replace any symbols or spaces with URL encoded entities<br>- Make sure Google can crawl your URL<br>- All images created using generative AI must contain meta data indicating that the image was AI-generated (for example, the IPTC [`DigitalSourceType`](https://cv.iptc.org/newscodes/digitalsourcetype/) [`TrainedAlgorithmicMedia`](https://cv.iptc.org/newscodes/digitalsourcetype/trainedAlgorithmicMedia) metadata tag). Don't remove embedded metadata tags such as the IPTC `DigitalSourceType` property from images created using generative AI tools, for example [Product Studio](https://support.google.com/merchants/answer/13708167). The following IPTC NewsCodes specify the type of digital source that was used to create the image, and should be preserved:<br>  <br>  <br>  <br>- [`TrainedAlgorithmicMedia`](https://cv.iptc.org/newscodes/digitalsourcetype/trainedAlgorithmicMedia): The image was created using a model derived from sampled content.<br>- [`CompositeSynthetic`](https://cv.iptc.org/newscodes/digitalsourcetype/compositeSynthetic): The image is a composite that includes synthetic elements.<br>- [`AlgorithmicMedia`](https://cv.iptc.org/newscodes/digitalsourcetype/algorithmicMedia): The image was created purely by an algorithm not based on any sampled training data (for example, an image created by software using a mathematical formula). |

## Marketplaces

These attributes are used to control how your product data is used if you are a marketplace and are using a multi-seller account.

|     |     |
| --- | --- |
| **Attributes and format** | **Requirements at a glance** |
| [External seller ID `[external_seller_id]`](https://support.google.com/merchants/answer/********)<br>![Required](https://storage.googleapis.com/support-kms-prod/ncoEh9hPBwKEuXOUKRffUwCyLKZVt7PeCt6w)**Required** for multi-seller account<br>Used by a marketplace to externally identify a seller. (For example, on a website)<br>**Example**<br>SellerPublicName1991<br>**Syntax**<br>1 - 50 characters<br>**Schema.org property**: No | - Use a unique value for each seller.<br>- Keep the ID the same when updating your data<br>- Use only valid characters. Avoid invalid characters like control, function, or private area characters<br>- Use the same ID for the same seller across countries or languages |

## Destinations

These attributes can be used to control the different locations where your content can appear. For example, you could use this attribute if you want a product to appear in a dynamic remarketing campaign, but not in a Shopping ads campaign.

| Attributes and format | Requirements at a glance |
| --- | --- |
| [Excluded destination `[excluded_destination]`](https://support.google.com/merchants/answer/********)<br>A setting that you can use to exclude a product from participating in a specific type of advertising campaign<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional**<br>**Example**<br>`Shopping_ads`<br>**Supported values**<br>- `Shopping_ads`<br>- `Buy_on_Google_listings`<br>- `Display_ads`<br>- `Local_inventory_ads`<br>- `Free_listings`<br>- `Free_local_listings`<br>- `YouTube_Shopping`<br>Some values only available for the classic version of Merchant Center.<br>**Schema.org property**: No |  |
| [Included destination `[included_destination]`](https://support.google.com/merchants/answer/12472550)<br>A setting that you can use to include a product in a specific type of advertising campaign<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional**<br>**Example**<br>`Shopping_ads`<br>**Supported values**<br>- Shopping\_ads<br>- Buy\_on\_Google\_listings<br>- Display\_ads<br>- Local\_inventory\_ads<br>- Free\_listings<br>- Free\_local\_listings<br>- YouTube\_Shopping<br>Some values only available for the classic version of Merchant Center.<br>**Schema.org property**: No |  |
| [Excluded countries for Shopping ads](https://support.google.com/merchants/answer/9837523) `[shopping_ads_excluded_country]`<br>A setting that allows you to exclude countries where your products are advertised on Shopping ads<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional**<br>Only available for Shopping ads<br>**Example**<br>DE<br>**Syntax**<br>2 characters. Must be an [ISO\_3166-1\_alpha-2](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) country code.<br>**Schema.org property**: No |  |
| [Pause `[pause]`](https://support.google.com/merchants/answer/12472831)<br>A setting you can use to pause and quickly reactivate a product for all ads (including Shopping ads, Display ads, and local inventory ads). A product can be paused for up to 14 days. If a product is paused for more than 14 days it will be disapproved. To re-approve, remove the attribute.<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional**<br>**Example**<br>`ads`<br>**Supported values**<br>`ads`<br>**Schema.org property**: No |  |

## Shipping

These attributes can be used together with the account shipping settings and return settings to help you provide accurate shipping and return costs. People who are shopping online rely on shipping costs and speeds, as well as return policies, to help them make choices about what to buy, so it's important to take the time to submit quality information.

| Attribute and format | Minimum requirements at a glance |
| --- | --- |
| [Shipping `[shipping]`](https://support.google.com/merchants/answer/********)<br>Your product's shipping cost, shipping speeds, and the locations your product ships to<br>![This icon represents whether the sourced content is dependent where the product attribute is used](https://storage.googleapis.com/support-kms-prod/wjVVtCqvAHd5rSOyJWrN0YpmnUV3ritl4686)**It depends**<br>![Required](https://storage.googleapis.com/support-kms-prod/ncoEh9hPBwKEuXOUKRffUwCyLKZVt7PeCt6w)**Shipping costs are required** for Shopping ads and free listings for the following countries: Australia, Austria, Belgium, Canada, Czechia, France, Germany, India, Ireland, Israel, Italy, New Zealand, Japan, the Netherlands, Poland, Romania, South Korea, Spain, Switzerland, the UK, and the US<br>You may also be required to provide shipping costs based on local laws or regulations.<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional** (to specify additional countries your product ships to or destinations where shipping costs are not required)<br>**Supported prices**<br>0–1000 USD ( [check for other currencies](https://support.google.com/merchants/answer/6324484))<br>**Example**<br>`US:CA:Overnight:16.00 USD:1:1:2:3`<br>**Syntax**<br>This attribute uses the following sub-attributes:<br>- Country `[country]` (Required)<br>  <br>  <br>   ISO 3166 country code<br>- Region `[region]`(Optional)<br>- Postal code `[postal_code]`(Optional)<br>- Location ID `[location_id]` (Optional)<br>- Location group name `[location_group_name]` (Optional)<br>- Service `[service]` (Optional)<br>  <br>  <br>   Service class or shipping speed<br>- Price `[price]` (Optional)<br>  <br>  <br>   Fixed shipping cost, including VAT if required<br>- Minimum handling time ` [min_handling_time] ` and maximum handling time `[max_handling_time]` (Optional)<br>  <br>  <br>   To specify handling time<br>- Minimum transit time ` [min_transit_time]` and maximum transit time `[max_transit_time]` (Optional)<br>  <br>  <br>   To specify transit time<br>**Schema.org property:** Yes (Learn more about [Merchant listing (Product, Offer) structured data](https://developers.google.com/search/docs/appearance/structured-data/merchant-listing) on Google Search Central) | - Use this setting when shipping costs for your product are not defined in your Merchant Center account or when you need to override shipping costs or speeds defined in your Merchant Center account.<br>- **Do not include government- imposed fees** such as import duties, recycling fees, copyright fees, or state-specific retail delivery fees in the shipping cost.<br>- **Include all additional fees that you charge as a merchant if they are not included in the product price**. Include the charges that aren't directly related to shipping but relevant for the purchase during checkout. For example, service, processing, activation, and handling charges. |
| [Shipping label ` [shipping_label]`](https://support.google.com/merchants/answer/6324504)<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional**<br>Label that you assign to a product to help assign correct shipping costs in Merchant Center account settings<br>**Example**<br>perishable<br>**Syntax**<br>Max 100 characters<br>**Schema.org property**: No | - Use a value that you'll recognize in your account shipping settings. The value won't be shown to customers. Examples:<br>  - Sameday<br>  - Oversize<br>  - Only FedEx |
| [Shipping weight `[shipping_weight]`](https://support.google.com/merchants/answer/********)<br>The weight of the product used to calculate the shipping cost<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional** (Required for carrier-calculated rates in your account shipping settings)<br>**Supported weights**<br>- 0–2000 lbs for imperial<br>- 0–1000 kgs for metric<br>**Example**<br>`3 kg`<br>**Syntax**<br>Number + unit<br>**Supported units**<br>- `lb`<br>- `oz`<br>- `g`<br>- `kg`<br>**Schema.org property**: No | - Submit this value if you set up account shipping settings for carrier-calculated rates or weight-based shipping services |
| [Shipping length `[shipping_length]`](https://support.google.com/merchants/answer/********)<br>The length of the product used to calculate the shipping cost by dimensional weight<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional** (Required for carrier-calculated rates in your account shipping settings)<br>**Example**<br>`20 in`<br>**Syntax**<br>Number + unit<br>**Supported values**<br>- 1 - 150 for inches<br>- 1 - 400 for cm<br>**Supported units**<br>- `in`<br>- `cm`<br>**Schema.org property**: No | - Submit this value if you set up account shipping settings for carrier-calculated rates.<br>- If you don't provide shipping dimension attributes while using carrier-calculated rates, Google won't be able to calculate rates based on the dimensional weight of the product. If that's the case, we'll just calculate the rates based on the value you provided in the shipping weight `[shipping_weight]` attribute.<br>- If you submit this attribute, submit all shipping dimension attributes:<br>  - Shipping length `[shipping_length]`<br>  - Shipping width `[shipping_width]`<br>  - Shipping height `[shipping_height]`<br>- Use the same unit for all shipping dimension attributes that apply to a single product.<br>- Google doesn't automatically calculate additional shipping cost for oversized products. If your package would be considered large or oversized by your carrier, you should use the shipping `[shipping]` attribute to set shipping cost for an individual product. |
| [Shipping width `[shipping_width]`](https://support.google.com/merchants/answer/********)<br>The width of the product used to calculate the shipping cost by dimensional weight<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional** (Required for carrier-calculated rates in your account shipping settings)<br>**Example**<br>`20 in`<br>**Syntax**<br>Number + unit<br>**Supported values**<br>- 1 - 150 for inches<br>- 1 - 400 for cm<br>**Supported units**<br>- `in`<br>- `cm`<br>**Schema.org property**: No | - Meet the requirements for the shipping length `[shipping_length]` attribute. |
| [Shipping height `[shipping_height]`](https://support.google.com/merchants/answer/********)<br>The height of the product used to calculate the shipping cost by dimensional weight<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional** (Required for carrier-calculated rates in your account shipping settings)<br>**Example**<br>`20 in`<br>**Syntax**<br>Number + unit<br>**Supported values**<br>- 1 - 150 for inches<br>- 1 - 400 for cm<br>**Supported units**<br>- `in`<br>- `cm`<br>**Schema.org property**: No | - Meet the requirements for the shipping length `[shipping_length]` attribute. |
| [Ships from country](https://support.google.com/merchants/answer/9837936) `[ships_from_country]`<br>A setting that allows you to provide the country from which your product will typically ship<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional**<br>**Example**<br>DE<br>**Syntax**<br>2 characters. Must be an [ISO\_3166-1\_alpha-2](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) country code.<br>**Schema.org property**: No | - Provide only the country from which you typically ship this product. |
| [Maximum handling time `[max_handling_time]`](https://support.google.com/merchants/answer/********)<br>The longest amount of time between when an order is placed for a product and when the product ships<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional**<br>**Example**<br>3<br>**Syntax**<br>Integer, greater than or equal to 0<br>**Schema.org property**: No | - Submit this attribute if you want to display the overall time it takes for a product to arrive at its destination.<br>- Submit the number of business days (as configured in Merchant Center).<br>- For products ready to be shipped the same day, submit 0.<br>- For submitting a time range submit maximum handling time `[max_handling_time]` in combination with minimum handling time `[min_handling_time]`. |
| [Shipping label `[shipping_label]`](https://support.google.com/merchants/answer/6324504)<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional**<br>Label that you assign to a product to help assign different transit times in Merchant Center account settings.<br>**Example**<br>From Seattle<br>**Syntax**<br>Max 100 characters<br>**Schema.org property**: No |  |
| [Minimum handling time `[min_handling_time]`](https://support.google.com/merchants/answer/********)<br>The shortest amount of time between when an order is placed for a product and when the product ships<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional**<br>**Example**<br>1<br>**Syntax**<br>Integer, greater than or equal to 0<br>**Schema.org property**: No | - Meet the requirements for the maximum handling time `[max_handling_time]` attribute. |
| [Free shipping threshold `[free_shipping_threshold]`](https://support.google.com/merchants/answer/********)<br>Order cost above which shipping is free.<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional**<br>**Example**<br>`US:16.00 USD`<br>**Syntax**<br>This attribute uses the following sub-attributes:<br>- Country `[country]` (Required)<br>  <br>  <br>   ISO 3166 country code<br>- Price threshold `[price_threshold]` (Required) Order cost above which shipping is free.<br>**Schema.org property**: No | - The free shipping threshold currency must be the same as the offer's price currency.<br>- The currency must be in the ISO 4217 format. For example, USD for US dollars.<br>- The decimal point must be a period (.). For example, 10.00 USD. |

## Tax

These attributes can be used together with the account tax settings to help you provide accurate tax costs.

| Format | Minimum requirements at a glance |
| --- | --- |
| [Tax `[tax]`](https://support.google.com/merchants/answer/6324454)<br>Your product’s sales tax rate in percent<br>![Required](https://storage.googleapis.com/support-kms-prod/ncoEh9hPBwKEuXOUKRffUwCyLKZVt7PeCt6w)**Required** (Available for the US only)<br>**Example**<br>`US:CA:5.00:y`<br>**Syntax**<br>This attribute uses 4 sub-attributes:<br>- Country `[country]` (optional)<br>  <br>  <br>   ISO 3166 country code<br>- Region `[region]` or postal code `[postal_code]` or location ID `[location_id]` (optional)<br>- Rate `[rate]` (required)<br>  <br>  <br>   Tax rate as a percentage<br>- Shipping tax `[tax_ship]` (optional)<br>  <br>  <br>   Specify if you charge tax on shipping.<br>   <br>  - Supported values:<br>    - `yes` or `no`<br>**Schema.org property**: No | - Use this setting only to override the account tax settings for an individual product. We recommend that you submit tax information for all your products using the account settings in Merchant Center.<br>- For the US and Canada:<br>  - Don't include tax in the price `[` `price]` attribute.<br>- For the US only, include the tax in the tax `[tax] ` attribute if you need to override your account settings.<br>- For all other countries:<br>  - Include value added tax (VAT) or Goods and Services Tax (GST) in the price attribute and do not use the tax attribute. |
| [Tax category `[tax_category]`](https://support.google.com/merchants/answer/********)<br>A category that classifies your product by specific tax rules<br>![Optional](https://storage.googleapis.com/support-kms-prod/p4E0rwqEBmdrMxeIBZGYPLtLvUYbXguSiydf)**Optional**(Recommended for custom tax rates at the account level)<br>**Example**<br>Apparel<br>**Syntax**<br>Max 100 characters<br>**Schema.org property**: No | - Use this attribute if you have products that have a specific tax rate.<br>- Category labels in your product data must match the labels you enter in the Categories section in Merchant Center. |

Give feedback about this article

Choose a section to give feedback on

## Was this helpful?

How can we improve it?

Submit

## Need more help?

### Try these next steps:

[Post to the help community  Get answers from community members](https://support.google.com/google-ads/threads?thread_filter=(category%3Agoogle_ads_google_shopping_merchant_center)) [Contact usTell us more and we’ll help you get there](https://support.google.com/merchants/gethelp)

true

[![](https://storage.googleapis.com/support-kms-prod/brYUzW7cjhgbnCIA0RzIkWFEDE0A8RjqrZaE)](https://business.google.com/advisors/?utm_campaign=MCHC&amp;utm_medium=MC&amp;utm_source=HelpCenter&amp;utm_content=Sidepanelpromo)

[Get help with Merchant Center setup from Small Business Advisors](https://business.google.com/advisors/?utm_campaign=MCHC&amp;utm_medium=MC&amp;utm_source=HelpCenter&amp;utm_content=Sidepanelpromo)

Want to receive one-on-one guidance and tailored recommendations on how to make the most out of Merchant Center? [Try booking an appointment with Small Business Advisors](https://business.google.com/advisors/?utm_campaign=MCHC&utm_medium=MC&utm_source=HelpCenter&utm_content=Sidepanelpromo).

**Important**: This service cannot troubleshoot issues, including approving Business Profile verification, resolving product disapprovals, account warnings or suspensions, or Google Ads billing.

2399092650387332914

true

Search

Clear search

Close search

Main menu

Google apps

Search Help Center

true

true

true

[Google Help](https://support.google.com/)

[Help Center](https://support.google.com/merchants/?hl=en)

[Get to know Merchant Center](https://support.google.com/merchants/topic/********?hl=en&ref_topic=7259405,) [Business settings](https://support.google.com/merchants/topic/********?hl=en&ref_topic=7259405,) [Upload your products](https://support.google.com/merchants/topic/********?hl=en&ref_topic=7259405,) [Product data spec](https://support.google.com/merchants/topic/7259406?hl=en&ref_topic=7259405,) [Market your products](https://support.google.com/merchants/topic/********?hl=en&ref_topic=7259405,) [Add-ons and additional features](https://support.google.com/merchants/topic/********?hl=en&ref_topic=7259405,) [Understand your performance](https://support.google.com/merchants/topic/********?hl=en&ref_topic=7259405,) [Troubleshoot](https://support.google.com/merchants/topic/********?hl=en&ref_topic=7259405,) [3rd party platform integrations](https://support.google.com/merchants/topic/********?hl=en&ref_topic=7259405,)

[Google Merchant Center](https://merchants.google.com/)

[Privacy Policy](https://www.google.com/intl/en/privacy.html) [Terms of Service](https://support.google.com/merchants/answer/160173)Submit feedback

[Get started](https://support.google.com/merchants/topic/15302229?hl=en&ref_topic=********,7259405,) [I need help with set up](https://support.google.com/merchants/topic/12671531?hl=en&ref_topic=********,7259405,) [Comparison Shopping Services](https://support.google.com/merchants/topic/12652782?hl=en&ref_topic=********,7259405,) [Policies and requirements](https://support.google.com/merchants/topic/7286989?hl=en&ref_topic=********,7259405,) [Glossary](https://support.google.com/merchants/table/15620279?hl=en&ref_topic=********,7259405,)

[Manage your sales tax settings](https://support.google.com/merchants/topic/12671521?hl=en&ref_topic=********,7259405,) [Manage your shipping settings](https://support.google.com/merchants/topic/12570808?hl=en&ref_topic=********,7259405,) [Manage your business settings](https://support.google.com/merchants/topic/12564660?hl=en&ref_topic=********,7259405,) [Manage integrations](https://support.google.com/merchants/topic/12564658?hl=en&ref_topic=********,7259405,)

[Add products](https://support.google.com/merchants/topic/12672304?hl=en&ref_topic=********,7259405,) [Maintain your product data](https://support.google.com/merchants/topic/12672214?hl=en&ref_topic=********,7259405,) [Set up rules for your product data sources](https://support.google.com/merchants/topic/14957981?hl=en&ref_topic=********,7259405,) [Create product data sources for Performance Max campaigns](https://support.google.com/merchants/topic/14958782?hl=en&ref_topic=********,7259405,) [Use AI and AR tools to help enhance product images](https://support.google.com/merchants/topic/15189223?hl=en&ref_topic=********,7259405,) [Troubleshoot product data source issues](https://support.google.com/merchants/topic/14957024?hl=en&ref_topic=********,7259405,) [Product data specifications](https://support.google.com/merchants/topic/14963864?hl=en&ref_topic=********,7259405,) [Best practices for different product situations](https://support.google.com/merchants/topic/15712169?hl=en&ref_topic=********,7259405,)

[Ad campaigns](https://support.google.com/merchants/topic/15191542?hl=en&ref_topic=********,7259405,) [Automated discounts](https://support.google.com/merchants/topic/15303099?hl=en&ref_topic=********,7259405,) [Free listings](https://support.google.com/merchants/topic/15190028?hl=en&ref_topic=********,7259405,) [Local inventory ads and free local listings](https://support.google.com/merchants/topic/14607119?hl=en&ref_topic=********,7259405,) [Prepare for big events](https://support.google.com/merchants/topic/15710958?hl=en&ref_topic=********,7259405,) [YouTube Shopping affiliate program](https://support.google.com/merchants/topic/14813437?hl=en&ref_topic=********,7259405,) [Promotions](https://support.google.com/merchants/topic/********?hl=en&ref_topic=********,7259405,) [YouTube Store](https://support.google.com/merchants/topic/15643752?hl=en&ref_topic=********,7259405,) [Vehicle ads](https://support.google.com/merchants/topic/********?hl=en&ref_topic=********,7259405,)

[Automated discounts](https://support.google.com/merchants/topic/15303099?hl=en&ref_topic=********,7259405,) [Google Customer Reviews](https://support.google.com/merchants/topic/14628801?hl=en&ref_topic=********,7259405,) [Local inventory ads and free local listings](https://support.google.com/merchants/topic/14607119?hl=en&ref_topic=********,7259405,) [Loyalty programs](https://support.google.com/merchants/topic/15164622?hl=en&ref_topic=********,7259405,) [Product Ratings](https://support.google.com/merchants/topic/14548703?hl=en&ref_topic=********,7259405,) [Product Studio](https://support.google.com/merchants/topic/********?hl=en&ref_topic=********,7259405,) [Promotions](https://support.google.com/merchants/topic/********?hl=en&ref_topic=********,7259405,) [Store Ratings](https://support.google.com/merchants/topic/********?hl=en&ref_topic=********,7259405,) [Vehicle ads](https://support.google.com/merchants/topic/********?hl=en&ref_topic=********,7259405,)

[About issues](https://support.google.com/merchants/topic/********?hl=en&ref_topic=********,7259405,)

[Manage your account with a Shopify integration](https://support.google.com/merchants/topic/********?hl=en&ref_topic=********,7259405,)

true

true

71525

false

false

## What is the issue with this selection?

Inaccurate - doesn't match what I see in the product

Hard to understand - unclear or translation is wrong

Missing info - relevant but not comprehensive

Irrelevant - doesn’t match the title and / or my expectations

Minor errors - formatting issues, typos, and / or broken links

Other suggestions - ideas to improve the content

## Share additional info or suggestions

​

​

Do not share any personal info

Cancel

Submit

By continuing, you agree Google uses your answers, [account & system info](https://support.google.com/merchants/answer/7052112?visit_id=638823643727659825-**********&hl=en&rd=1#) to improve services, per our [Privacy](https://myaccount.google.com/privacypolicy?hl=en) & [Terms](https://policies.google.com/terms?hl=en).

false

false