import os
import logging
import torch
import numpy as np
import subprocess
import sys
import platform
from sentence_transformers import SentenceTransformer


def check_pytorch_cuda_installation():
    """检查PyTorch CUDA安装情况并记录详细信息"""
    logging.info("===== PyTorch CUDA 安装检查 =====")

    # 检查Python版本
    logging.info(f"Python版本: {platform.python_version()}")

    # 检查PyTorch版本
    logging.info(f"PyTorch版本: {torch.__version__}")

    # 检查CUDA可用性
    cuda_available = torch.cuda.is_available()
    logging.info(f"CUDA可用性: {cuda_available}")

    # 如果CUDA可用，获取更多信息
    if cuda_available:
        logging.info(f"CUDA版本: {torch.version.cuda}")
        logging.info(
            f"cuDNN版本: {torch.backends.cudnn.version() if torch.backends.cudnn.is_available() else '不可用'}"
        )
        logging.info(f"CUDA设备数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            logging.info(f"CUDA设备 {i}: {torch.cuda.get_device_name(i)}")
    else:
        logging.warning("CUDA不可用，检查可能的原因...")

        # 检查PyTorch是否包含CUDA
        if "+cu" in torch.__version__ or "cuda" in torch.__version__:
            logging.info("PyTorch版本包含CUDA支持")
        else:
            logging.warning(
                "PyTorch版本可能不包含CUDA支持，考虑重新安装支持CUDA的PyTorch版本"
            )

        # 检查CUDA_HOME环境变量
        cuda_home = os.environ.get("CUDA_HOME") or os.environ.get("CUDA_PATH")
        if cuda_home:
            logging.info(f"CUDA_HOME环境变量: {cuda_home}")
        else:
            logging.warning("CUDA_HOME环境变量未设置")

        # 检查系统PATH中的CUDA
        path = os.environ.get("PATH", "")
        cuda_in_path = any("cuda" in p.lower() for p in path.split(os.pathsep))
        logging.info(f"CUDA在系统PATH中: {cuda_in_path}")

        # 尝试运行nvcc命令检查CUDA工具链
        try:
            nvcc_output = subprocess.check_output(
                ["nvcc", "--version"], stderr=subprocess.STDOUT, text=True
            )
            logging.info(f"NVCC版本信息: {nvcc_output.strip()}")
        except (subprocess.SubprocessError, FileNotFoundError):
            logging.warning("无法运行nvcc命令，CUDA工具链可能未正确安装或不在PATH中")

        # 尝试运行nvidia-smi命令检查GPU驱动
        try:
            nvidia_smi_output = subprocess.check_output(
                ["nvidia-smi"], stderr=subprocess.STDOUT, text=True
            )
            logging.info("NVIDIA驱动已安装，nvidia-smi输出:")
            for line in nvidia_smi_output.split("\n")[:10]:  # 只显示前10行
                logging.info(line)
        except (subprocess.SubprocessError, FileNotFoundError):
            logging.warning("无法运行nvidia-smi命令，NVIDIA驱动可能未安装或不兼容")

    logging.info("===== PyTorch CUDA 检查完成 =====")
    return cuda_available


# 设置环境变量以优化性能
os.environ["TOKENIZERS_PARALLELISM"] = "false"


class CustomSentenceTransformer:
    """
    自定义SentenceTransformer包装类，解决meta tensor错误
    """

    def __init__(
        self, model_name="paraphrase-multilingual-mpnet-base-v2", device="auto"
    ):
        """
        初始化SentenceTransformer模型

        参数:
            model_name (str): 模型名称
            device (str): 设备名称，可以是'cpu'、'cuda'或'auto'
                          'auto'会自动选择可用的最佳设备
        """
        self.model_name = model_name
        # 添加name属性，ChromaDB需要这个属性
        self.name = f"CustomSentenceTransformer_{model_name}"
        logging.info(f"开始加载SentenceTransformer模型: {model_name}")

        # 设置设备
        self.device = device  # 保留原始设备设置，在_load_model_safely中处理

        # 尝试多种方法加载模型
        self.model = self._load_model_safely()

    def _load_model_safely(self):
        """使用多种方法尝试加载模型，处理各种可能的错误"""
        # 方法1: 直接加载
        try:
            # 增强CUDA检测逻辑
            if self.device == "auto":
                # 详细检查CUDA可用性
                cuda_available = torch.cuda.is_available()
                cuda_device_count = torch.cuda.device_count() if cuda_available else 0

                if cuda_available and cuda_device_count > 0:
                    device = "cuda"
                    cuda_device_name = torch.cuda.get_device_name(0)
                    logging.info(f"检测到CUDA可用，将使用GPU: {cuda_device_name}")
                    logging.info(f"CUDA版本: {torch.version.cuda}")
                    logging.info(f"可用GPU数量: {cuda_device_count}")
                else:
                    device = "cpu"
                    logging.info(f"未检测到CUDA，将使用CPU")
                    logging.info(f"CUDA可用性: {cuda_available}")
                    logging.info(f"PyTorch版本: {torch.__version__}")
                    # 检查CUDA_HOME环境变量
                    cuda_home = os.environ.get("CUDA_HOME") or os.environ.get(
                        "CUDA_PATH"
                    )
                    if cuda_home:
                        logging.info(f"CUDA_HOME环境变量: {cuda_home}")
                    else:
                        logging.info("CUDA_HOME环境变量未设置")
            else:
                device = self.device

            # 再次检查设备可用性
            if device == "cuda" and not torch.cuda.is_available():
                logging.warning(f"请求使用CUDA设备，但CUDA不可用，自动切换到CPU")
                device = "cpu"

            logging.info(f"尝试方法1: 直接加载模型到{device}设备")
            try:
                model = SentenceTransformer(self.model_name, device=device)
                logging.info(f"方法1成功: 模型加载完成，使用设备: {device}")
                return model
            except RuntimeError as re:
                # 捕获CUDA特定错误
                if "CUDA" in str(re) and device == "cuda":
                    logging.warning(f"CUDA错误: {str(re)}")
                    logging.warning("尝试回退到CPU设备")
                    device = "cpu"
                    model = SentenceTransformer(self.model_name, device=device)
                    logging.info(f"回退到CPU成功: 模型加载完成，使用设备: {device}")
                    return model
                else:
                    # 其他运行时错误，继续尝试其他方法
                    raise
        except Exception as e:
            logging.warning(f"方法1失败: {str(e)}")
            # 如果是CUDA相关错误，记录更多信息
            if "CUDA" in str(e) or "cuda" in str(e):
                logging.warning("检测到CUDA相关错误，记录系统信息:")
                logging.warning(f"PyTorch版本: {torch.__version__}")
                if hasattr(torch, "version") and hasattr(torch.version, "cuda"):
                    logging.warning(f"PyTorch CUDA版本: {torch.version.cuda}")
                logging.warning(f"CUDA可用性: {torch.cuda.is_available()}")
                if torch.cuda.is_available():
                    logging.warning(f"CUDA设备数量: {torch.cuda.device_count()}")
                    logging.warning(f"当前CUDA设备: {torch.cuda.current_device()}")
                    logging.warning(f"CUDA设备名称: {torch.cuda.get_device_name(0)}")

        # 方法2: 使用to_empty()方法 (适用于较新版本的PyTorch)
        try:
            logging.info("尝试方法2: 使用to_empty()方法")
            # 先创建模型
            model = SentenceTransformer(self.model_name)
            # 使用to_empty()方法移动到设备
            if hasattr(model, "to_empty"):
                model = model.to_empty().to(self.device)
            else:
                # 如果模型没有to_empty方法，尝试手动实现
                for module in model.modules():
                    if hasattr(module, "to_empty"):
                        module = module.to_empty().to(self.device)
            logging.info("方法2成功: 模型加载完成")
            return model
        except Exception as e:
            logging.warning(f"方法2失败: {str(e)}")

        # 方法3: 使用CPU设备并禁用CUDA
        try:
            logging.info("尝试方法3: 强制使用CPU设备并禁用CUDA")
            # 临时禁用CUDA
            old_cuda_visible_devices = os.environ.get("CUDA_VISIBLE_DEVICES", "")
            os.environ["CUDA_VISIBLE_DEVICES"] = ""

            model = SentenceTransformer(self.model_name, device="cpu")

            # 恢复CUDA设置
            if old_cuda_visible_devices:
                os.environ["CUDA_VISIBLE_DEVICES"] = old_cuda_visible_devices
            else:
                os.environ.pop("CUDA_VISIBLE_DEVICES", None)

            logging.info("方法3成功: 模型加载完成")
            return model
        except Exception as e:
            logging.warning(f"方法3失败: {str(e)}")

            # 恢复CUDA设置
            if old_cuda_visible_devices:
                os.environ["CUDA_VISIBLE_DEVICES"] = old_cuda_visible_devices
            else:
                os.environ.pop("CUDA_VISIBLE_DEVICES", None)

        # 方法4: 使用较低级别的PyTorch API
        try:
            logging.info("尝试方法4: 使用较低级别的PyTorch API")
            # 使用较低级别的API加载模型
            from transformers import AutoModel, AutoTokenizer

            tokenizer = AutoTokenizer.from_pretrained(self.model_name)
            base_model = AutoModel.from_pretrained(self.model_name)

            # 创建一个简单的包装类
            class SimpleTransformer:
                def __init__(self, tokenizer, model):
                    self.tokenizer = tokenizer
                    self.model = model
                    self.model.eval()

                def encode(
                    self, sentences, convert_to_numpy=True, normalize_embeddings=True
                ):
                    # 将输入转换为列表
                    if isinstance(sentences, str):
                        sentences = [sentences]

                    # 对输入进行编码
                    inputs = self.tokenizer(
                        sentences, padding=True, truncation=True, return_tensors="pt"
                    )

                    # 移动到正确的设备
                    for key in inputs:
                        inputs[key] = inputs[key].to(self.model.device)

                    # 获取模型输出
                    with torch.no_grad():
                        outputs = self.model(**inputs)

                    # 使用最后一层隐藏状态的平均值作为句子嵌入
                    embeddings = torch.mean(outputs.last_hidden_state, dim=1)

                    # 归一化嵌入
                    if normalize_embeddings:
                        embeddings = torch.nn.functional.normalize(
                            embeddings, p=2, dim=1
                        )

                    # 转换为numpy数组
                    if convert_to_numpy:
                        embeddings = embeddings.cpu().numpy()

                    return embeddings

            model = SimpleTransformer(tokenizer, base_model)
            logging.info("方法4成功: 模型加载完成")
            return model
        except Exception as e:
            logging.warning(f"方法4失败: {str(e)}")

        # 如果所有方法都失败，抛出异常
        error_msg = "所有加载模型的方法都失败，无法初始化嵌入模型"
        logging.error(error_msg)
        raise RuntimeError(error_msg)

    def encode(self, texts, convert_to_numpy=True, normalize_embeddings=True):
        """
        将文本编码为向量

        参数:
            texts (str or list): 要编码的文本或文本列表
            convert_to_numpy (bool): 是否将结果转换为numpy数组
            normalize_embeddings (bool): 是否对嵌入向量进行归一化

        返回:
            numpy.ndarray or list: 编码后的向量
        """
        try:
            # 调用模型的encode方法
            embeddings = self.model.encode(
                texts,
                convert_to_numpy=convert_to_numpy,
                normalize_embeddings=normalize_embeddings,
            )
            return embeddings
        except Exception as e:
            logging.error(f"编码文本时出错: {str(e)}")
            raise

    def __call__(self, input):
        """
        使类实例可调用，兼容ChromaDB的嵌入函数接口

        参数:
            input (list): 要编码的文本列表

        返回:
            list: 编码后的向量列表
        """
        try:
            # 适配新的ChromaDB接口，接受input参数而不是texts
            embeddings = self.encode(input)
            # 确保返回的是列表格式
            if isinstance(embeddings, np.ndarray):
                return embeddings.tolist()
            return embeddings
        except Exception as e:
            logging.error(f"调用嵌入函数时出错: {str(e)}")
            raise


def get_embedding_function(
    model_name="paraphrase-multilingual-mpnet-base-v2", device="auto", force_cuda=False
):
    """
    获取嵌入函数，用于ChromaDB

    参数:
        model_name (str): 模型名称
        device (str): 设备名称，可以是'cpu'、'cuda'或'auto'
                      'auto'会自动选择可用的最佳设备
        force_cuda (bool): 是否强制使用CUDA，即使自动检测不可用

    返回:
        callable: 嵌入函数
    """
    try:
        # 首先运行CUDA安装检查，获取详细信息
        cuda_available = check_pytorch_cuda_installation()
        # 根据CUDA检查结果和用户设置决定使用哪个设备
        if force_cuda:
            if cuda_available:
                logging.info("强制使用CUDA模式，且CUDA可用")
                device = "cuda"
            else:
                logging.warning("强制使用CUDA模式，但CUDA不可用")
                logging.warning("PyTorch可能未安装CUDA支持版本或CUDA驱动有问题")
                logging.warning("回退到CPU模式")
                device = "cpu"
        elif device == "auto":
            # 自动模式下，根据CUDA可用性选择设备
            if cuda_available:
                logging.info("自动模式选择CUDA设备")
                device = "cuda"
            else:
                logging.info("自动模式选择CPU设备（CUDA不可用）")
                device = "cpu"
        elif device == "cuda" and not cuda_available:
            # 明确要求CUDA但不可用
            logging.warning("明确要求CUDA设备，但CUDA不可用，回退到CPU")
            device = "cpu"

        # 创建自定义SentenceTransformer
        transformer = CustomSentenceTransformer(model_name=model_name, device=device)
        logging.info(f"成功创建嵌入函数: {model_name}")
        return transformer
    except Exception as e:
        logging.error(f"创建嵌入函数失败: {str(e)}")

        # 如果强制使用CUDA失败，尝试回退到CPU
        if force_cuda:
            logging.warning("强制使用CUDA失败，尝试回退到CPU")
            try:
                transformer = CustomSentenceTransformer(
                    model_name=model_name, device="cpu"
                )
                logging.info(f"成功创建嵌入函数(CPU回退): {model_name}")
                return transformer
            except Exception as e2:
                logging.error(f"回退到CPU也失败: {str(e2)}")

        raise
