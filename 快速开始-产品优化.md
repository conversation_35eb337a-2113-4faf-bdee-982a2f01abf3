# 产品信息AI优化 - 快速开始指南

## 🚀 功能简介

这个功能可以自动优化您的Shopify产品信息，使其符合谷歌GMC（Google Merchant Center）政策规范：

- ✅ **优化产品标题** - 移除夸大词汇，使用专业描述
- ✅ **优化产品描述** - 保留图片和技术信息，移除销售压力用语
- ✅ **AI优化Handle** - 基于原Handle和优化标题，生成SEO友好的英文URL

## ⚡ 快速开始

### 1. 配置AI模型

在 `config.json` 中添加您的AI模型配置：

```json
{
  "models": {
    "qwen": {
      "name": "Qwen3-235B-A22B",
      "api_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
      "api_key": "your-api-key-here",
      "model_id": "qwen3-235b-a22b",
      "max_tokens": 4096,
      "temperature": 0.1
    }
  },
  "active_model": "qwen"
}
```

### 2. 运行优化

```bash
python 批量修改产品信息.py
```

选择：
1. 选择 "1. 批量修改所有产品" 或 "2. 只修改单个产品"
2. 选择 "5. AI优化产品信息（标题、描述、Handle）符合GMC政策规范"
3. 确认开始优化
4. 设置线程数（建议1-3个）

### 3. 查看结果

系统会显示：
- 原始产品信息
- 优化后的信息
- 更新状态和产品链接

## 📋 优化示例

### 标题优化

**优化前：**
```
AMAZING SUPER BRIGHT LED BULB 100W EQUIVALENT BEST QUALITY GUARANTEED!!!
```

**优化后：**
```
LED Light Bulb 100W Equivalent Daylight White Energy Efficient
```

### 描述优化

**优化前：**
```html
<p>This is the BEST LED light bulb you will EVER find! GUARANTEED!</p>
<p>🔥 SUPER SALE PRICE! LIMITED TIME ONLY! 🔥</p>
<p>Size: 4.5" x 2.3"</p>
<img src="bulb-size.jpg" alt="size">
<p>⚡ BUY NOW and SAVE BIG! ⚡</p>
```

**优化后：**
```html
<p>Energy-efficient LED light bulb with 100W equivalent brightness.</p>
<p>Product specifications: 4.5" height x 2.3" diameter, 25,000-hour lifespan.</p>
<img src="bulb-size.jpg" alt="size">
<p>Suitable for residential and commercial lighting applications.</p>
```

### Handle优化

**优化前：**
```
amazing-super-bright-led-bulb-100w-equivalent-best-quality-guaranteed-lighting-bulb
```

**优化后：**
```
led-bulb-100w-daylight-white-energy-efficient
```

**改进点：**
- 移除夸大词汇（amazing, super, best, guaranteed）
- 移除产品类型信息（lighting）
- 使用英文，长度优化
- 专注核心产品特征

## 🔧 支持的AI模型

- **阿里云通义千问** (Qwen)
- **DeepSeek**
- **Google Gemini**
- **SiliconFlow**
- **其他OpenAI兼容API**

## ⚠️ 注意事项

1. **API配额** - 确保AI模型API有足够配额
2. **网络连接** - 保持稳定的网络连接
3. **备份数据** - 建议先在测试环境验证
4. **批量处理** - 大量产品建议分批处理

## 🧪 测试功能

运行演示查看效果：
```bash
python demo_product_optimizer.py
```

运行完整测试：
```bash
python test_product_optimizer.py
```

## 🆘 常见问题

### Q: AI模型调用失败怎么办？
A: 检查API密钥、网络连接和配额余额

### Q: 优化后的内容不满意？
A: 可以调整AI模型的temperature参数，或手动微调

### Q: 图片会丢失吗？
A: 不会，系统会自动保留所有图片标签和位置

### Q: Handle冲突怎么办？
A: 系统会自动处理冲突，生成唯一的Handle

### Q: Handle优化后不是英文怎么办？
A: 系统有多重检查机制，确保输出英文，如AI失败会回退到基础生成方法

### Q: 优化后的Handle太短或太长？
A: 系统会自动控制长度在3-50字符之间，确保符合Shopify规范

## 📞 技术支持

- 📚 详细文档：`产品优化功能说明.md`
- 🧪 测试脚本：`test_product_optimizer.py`
- 🎯 演示脚本：`demo_product_optimizer.py`

---

**开始优化您的产品信息，提升Google Shopping表现！** 🎉
