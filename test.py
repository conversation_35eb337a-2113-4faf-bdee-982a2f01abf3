import requests
import json

# 飞书API配置
FEISHU_APP_ID = "cli_a5a12cc72c78500c"
FEISHU_APP_SECRET = "WBP6rHyvPjeLe6qdPr84Texj1h2uc3Cw"
FEISHU_SHEET_TOKEN = "Xq4LsFeBhh9afotkOswcLr2tnqe"
FEISHU_PRODUCT_SHEET_ID = "7d8ce1"


def get_feishu_access_token():
    """获取飞书访问令牌"""
    url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
    headers = {"Content-Type": "application/json"}
    data = {"app_id": FEISHU_APP_ID, "app_secret": FEISHU_APP_SECRET}

    try:
        response = requests.post(url, headers=headers, data=json.dumps(data), timeout=30)
        response_json = response.json()
        
        if response.status_code == 200 and response_json.get("tenant_access_token"):
            return response_json.get("tenant_access_token")
        return None
    except Exception:
        return None


def get_next_empty_row(spreadsheet_token, sheet_id, token):
    """获取下一个空行的行号"""
    url = f"https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/{spreadsheet_token}/values/{sheet_id}!A:A"
    headers = {"Authorization": f"Bearer {token}"}

    try:
        response = requests.get(url, headers=headers, timeout=30)
        if response.status_code == 200:
            values = response.json().get("data", {}).get("valueRange", {}).get("values", [])
            return len(values) + 1
        return 2
    except Exception:
        return 2


def write_to_feishu_sheet(spreadsheet_token, sheet_id, product_data, issue_data):
    """将数据追加写入飞书表格"""
    # 获取访问令牌
    token = get_feishu_access_token()
    if not token:
        return False

    # 获取下一个空行
    next_row = get_next_empty_row(spreadsheet_token, sheet_id, token)

    # 构造行数据
    values = [
        product_data.get("id", ""),
        product_data.get("title", ""),
        product_data.get("description", ""),
        product_data.get("link", ""),
        issue_data.get("severity", ""),
        issue_data.get("description", ""),
        issue_data.get("fix_suggestion", "")
    ]

    # 发送API请求
    url = f"https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/{spreadsheet_token}/values"
    headers = {"Content-Type": "application/json", "Authorization": f"Bearer {token}"}
    payload = {"valueRange": {"range": f"{sheet_id}!A{next_row}:G{next_row}", "values": [values]}}

    try:
        response = requests.put(url, headers=headers, json=payload, timeout=60)
        if response.status_code == 200 and response.json().get("code", 0) == 0:
            return True
        return False
    except Exception:
        return False


# 测试
if __name__ == "__main__":
    test_product = {
        "id": "TEST-001",
        "title": "测试产品",
        "description": "测试描述",
        "link": "https://example.com/test"
    }

    test_issue = {'severity': '低', 'description': "具体问题描述\n1. [低] 产品标题中使用了创意性词汇 'Creative'，虽然不违规，但谷歌更倾向清晰、直白的描述。\n2. [低] 产品描述中的尺寸信息包含两个选项，可能会造成混淆。建议明确主推尺寸或标注为可选。\n3. [低] 描述中使用了营销性语言如'elegant lighting solution'，虽不违反政策，但应尽量客观描述产品属性。", 'fix_suggestion': '具体修复建议，如：\n1. [低] 产品标题使用创意性词汇 \'Creative\'：Creative Crystal Pendant Light\n修复建议: 将标题改为更直接的描述，例如：Crystal Pendant Light - Modern Chrome Design\n2. [低] 产品描述中的尺寸有两个选项：20cm D x 20cm H (7.9" D x 7.9" H) or 30cm D x 30cm H (11.8" D x 11.8" H)\n修复建议: 明确主推尺寸并标注可定制，例如：Available in 20cm D x 20cm H (7.9" D x 7.9" H) with custom sizing options.\n3. [低] 使用营销性语言如\'elegant lighting solution\'\n修复建议: 将\'elegant lighting solution\'改为更客观描述，例如：functional and stylish lighting option 或 decorative lighting fixture suitable for modern interiors'}

    result = write_to_feishu_sheet(
        spreadsheet_token=FEISHU_SHEET_TOKEN,
        sheet_id=FEISHU_PRODUCT_SHEET_ID,
        product_data=test_product,
        issue_data=test_issue
    )

    print("✓ 追加写入成功!" if result else "× 追加写入失败!")
