#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
产品优化器演示脚本
展示如何使用AI优化产品信息功能
"""

import json
import threading
import time
from product_optimizer import ProductOptimizer


class MockRateLimiter:
    """模拟速率限制器"""
    def wait_if_needed(self):
        time.sleep(0.1)  # 模拟API限制


def load_config():
    """加载配置文件"""
    try:
        with open("config.json", "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception as e:
        print(f"加载配置文件失败: {str(e)}")
        print("请确保config.json文件存在且配置正确")
        return None


def demo_single_optimization():
    """演示单个产品优化"""
    print("=" * 80)
    print("🚀 产品信息AI优化演示")
    print("=" * 80)

    # 加载配置
    config = load_config()
    if not config:
        return

    # 检查AI模型配置
    if not config.get("models") or not config.get("active_model"):
        print("❌ 未检测到AI模型配置")
        print("请在config.json中配置models和active_model字段")
        return

    print(f"✅ 使用AI模型: {config.get('active_model')}")

    # 创建优化器
    rate_limiter = MockRateLimiter()
    print_lock = threading.Lock()
    optimizer = ProductOptimizer(config, rate_limiter, print_lock)

    # 模拟产品数据
    demo_product = {
        "title": "AMAZING SUPER BRIGHT LED BULB 100W EQUIVALENT BEST QUALITY GUARANTEED!!!",
        "handle": "amazing-super-bright-led-bulb-100w-equivalent-best-quality-guaranteed-lighting-bulb",
        "description": """
        <p>This is the MOST AMAZING LED bulb you will EVER buy! GUARANTEED to be the BEST!</p>
        <p>🔥 SUPER SALE PRICE! LIMITED TIME ONLY! 🔥</p>
        <p>Features:</p>
        <ul>
            <li>ULTRA BRIGHT 100W equivalent</li>
            <li>SAVES MONEY on electricity bills</li>
            <li>LASTS FOREVER - 25,000 hours!</li>
            <li>Size: 4.5 inches tall x 2.3 inches wide</li>
        </ul>
        <img src="https://example.com/led-bulb-dimensions.jpg" alt="LED bulb size comparison">
        <p>Perfect for ANY room! Kitchen, bedroom, living room, office!</p>
        <p>⚡ BUY NOW and SAVE BIG! Don't miss this INCREDIBLE deal! ⚡</p>
        """,
        "product_type": "lighting",
        "vendor": "BrightLight Co"
    }

    print("\n📋 原始产品信息:")
    print("-" * 50)
    print(f"标题: {demo_product['title']}")
    print(f"Handle: {demo_product['handle']}")
    print(f"类型: {demo_product['product_type']}")
    print(f"品牌: {demo_product['vendor']}")
    print(f"描述: {demo_product['description'][:200]}...")

    print("\n🤖 开始AI优化...")
    print("-" * 50)

    # 使用综合优化方法一次性优化所有字段
    print("正在进行AI综合优化（标题、描述、Handle）...")
    optimization_result = optimizer.optimize_product_comprehensive(
        demo_product['title'],
        demo_product['description'],
        demo_product['handle'],
        demo_product['vendor']
    )

    optimized_title = optimization_result["title"]
    optimized_description = optimization_result["description"]
    optimized_handle = optimization_result["handle"]

    print("\n✨ 优化结果:")
    print("=" * 80)

    # 显示标题对比
    print("📝 标题优化:")
    print(f"原始: {demo_product['title']}")
    print(f"优化: {optimized_title}")
    print(f"改进: 移除夸大词汇，使用专业描述，符合GMC规范")

    print("\n" + "-" * 50)

    # 显示描述对比
    print("📄 描述优化:")
    print("原始描述包含:")
    print("- 夸大宣传词汇 (AMAZING, BEST, GUARANTEED)")
    print("- 销售压力用语 (LIMITED TIME, BUY NOW)")
    print("- 过度使用表情符号和大写字母")

    print("\n优化后描述:")
    clean_desc = optimizer._clean_html_for_display(optimized_description)
    print(f"{clean_desc[:300]}...")

    print("\n改进点:")
    print("- 使用客观、专业的产品描述")
    print("- 保留重要的技术规格信息")
    print("- 保留产品尺寸图片引用")
    print("- 符合GMC内容政策要求")

    print("\n" + "-" * 50)

    # 显示Handle优化
    print("🔗 Handle优化:")
    print(f"原始Handle: {demo_product['handle']}")
    print(f"优化Handle: {optimized_handle}")
    print("改进点:")
    print("- 移除产品类型信息，专注产品特征")
    print("- 使用英文，利于SEO")
    print("- 长度优化，更简洁易记")
    print("- 符合Shopify URL规范")

    print("\n" + "=" * 80)
    print("✅ 优化完成！产品信息现在符合谷歌GMC政策规范")
    print("\n🔍 优化说明:")
    print("- 基于AI模型的内置知识进行优化")
    print("- 遵循谷歌GMC政策规范要求")
    print("- 移除夸大宣传和销售压力用语")
    print("- 保留重要的产品技术信息")


def show_configuration_guide():
    """显示配置指南"""
    print("\n" + "=" * 80)
    print("⚙️ 配置指南")
    print("=" * 80)

    print("\n1. AI模型配置 (config.json):")
    print("-" * 30)
    config_example = {
        "models": {
            "qwen": {
                "name": "Qwen3-235B-A22B",
                "api_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
                "api_key": "your-api-key-here",
                "model_id": "qwen3-235b-a22b",
                "max_tokens": 4096,
                "temperature": 0.1
            }
        },
        "active_model": "qwen"
    }
    print(json.dumps(config_example, indent=2, ensure_ascii=False))

    print("\n2. Shopify API配置:")
    print("-" * 20)
    print("确保config.json中包含有效的Shopify API凭据")

    print("\n3. 使用方法:")
    print("-" * 10)
    print("python 批量修改产品信息.py")
    print("选择操作 5: AI优化产品信息")

    print("\n4. 优化特性:")
    print("-" * 10)
    print("- 使用AI模型内置知识优化产品信息")
    print("- 自动符合谷歌GMC政策规范")
    print("- 保留产品图片和重要技术信息")
    print("- 生成SEO友好的产品Handle")


def main():
    """主函数"""
    print("🎯 欢迎使用产品信息AI优化功能")

    try:
        # 运行演示
        demo_single_optimization()

        # 显示配置指南
        show_configuration_guide()

    except KeyboardInterrupt:
        print("\n\n👋 演示已取消")
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

    print("\n" + "=" * 80)
    print("📚 更多信息请查看: 产品优化功能说明.md")
    print("🧪 运行测试: python test_product_optimizer.py")
    print("=" * 80)


if __name__ == "__main__":
    main()
