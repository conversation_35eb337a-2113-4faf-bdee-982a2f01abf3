#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试产品变体属性替换功能
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入主模块的函数
from 批量修改产品信息 import update_product_variant_options, get_auth_headers, SHOP_NAME
import requests

def test_single_product_options(product_id):
    """测试单个产品的属性信息"""
    print(f"=== 测试产品 {product_id} 的属性信息 ===")
    
    # 获取产品详情
    url = f"https://{SHOP_NAME}.myshopify.com/admin/api/2023-04/products/{product_id}.json"
    headers = get_auth_headers()
    
    try:
        resp = requests.get(url, headers=headers, verify=False, timeout=10)
        
        if resp.status_code == 200:
            product_data = resp.json().get("product", {})
            options = product_data.get("options", [])
            variants = product_data.get("variants", [])
            
            print(f"产品标题: {product_data.get('title', 'N/A')}")
            print(f"产品ID: {product_id}")
            print(f"属性数量: {len(options)}")
            print("当前属性:")
            for i, option in enumerate(options):
                print(f"  {i+1}. {option.get('name', 'N/A')} (位置: {option.get('position', 'N/A')})")
                values = option.get('values', [])
                print(f"     值: {', '.join(values) if values else 'N/A'}")
            
            print(f"\n变体数量: {len(variants)}")
            if variants:
                print("前3个变体的选项值:")
                for i, variant in enumerate(variants[:3]):
                    option_values = []
                    for j in range(1, 4):
                        value = variant.get(f'option{j}')
                        if value:
                            option_values.append(f"option{j}: {value}")
                    print(f"  变体 {i+1}: {', '.join(option_values)}")
            
            return True
        else:
            print(f"获取产品详情失败: HTTP {resp.status_code}")
            print(f"响应: {resp.text}")
            return False
            
    except Exception as e:
        print(f"获取产品详情时发生异常: {e}")
        return False

def test_option_replacement(product_id, old_name, new_name):
    """测试属性名称替换"""
    print(f"\n=== 测试属性替换: '{old_name}' → '{new_name}' ===")
    
    # 先显示替换前的状态
    print("替换前:")
    test_single_product_options(product_id)
    
    # 执行替换
    print(f"\n执行替换...")
    success = update_product_variant_options(product_id, old_name, new_name)
    
    if success:
        print(f"替换操作返回成功")
        # 显示替换后的状态
        print("\n替换后:")
        test_single_product_options(product_id)
    else:
        print(f"替换操作失败")
    
    return success

def main():
    """主函数"""
    print("产品变体属性替换功能测试")
    print("=" * 50)
    
    # 获取用户输入
    try:
        product_id = input("请输入要测试的产品ID: ").strip()
        if not product_id.isdigit():
            print("产品ID必须是数字")
            return
        
        product_id = int(product_id)
        
        # 先查看产品当前状态
        if not test_single_product_options(product_id):
            print("无法获取产品信息，退出测试")
            return
        
        # 询问是否要进行替换测试
        test_replace = input("\n是否要测试属性替换功能？(y/n): ").strip().lower()
        if test_replace == 'y':
            old_name = input("请输入要替换的旧属性名称: ").strip()
            new_name = input("请输入新的属性名称: ").strip()
            
            if old_name and new_name:
                test_option_replacement(product_id, old_name, new_name)
            else:
                print("属性名称不能为空")
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
