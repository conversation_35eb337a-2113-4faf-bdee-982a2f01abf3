#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简化后的SEO清理功能
"""

import re
from html import unescape

def clean_html_for_seo_simple(html_content, max_length=160):
    """
    简化版的HTML清理函数 - 只移除HTML标签，保留所有文本内容
    """
    if not html_content:
        return ""

    # 移除HTML标签，保留所有文本内容
    clean_text = re.sub(r"<[^>]+>", "", html_content)
    
    # 解码HTML实体（如 &amp; → &, &quot; → "）
    clean_text = unescape(clean_text)
    
    # 标准化空白字符（将多个空格、换行等合并为单个空格）
    clean_text = re.sub(r"\s+", " ", clean_text).strip()
    
    # 只移除控制字符，保留所有可见字符和标点符号
    clean_text = re.sub(r"[\x00-\x1f\x7f-\x9f]", "", clean_text)
    
    # 截断到指定长度
    if len(clean_text) > max_length:
        # 在单词边界截断，避免截断单词中间
        truncated = clean_text[:max_length]
        last_space = truncated.rfind(" ")
        if last_space > max_length * 0.8:  # 如果最后一个空格位置合理
            clean_text = truncated[:last_space] + "..."
        else:
            clean_text = truncated + "..."

    return clean_text

def clean_html_for_seo_old(html_content, max_length=160):
    """
    旧版本的清理函数（有符号丢失问题）
    """
    if not html_content:
        return ""

    # 移除HTML标签
    clean_text = re.sub(r"<[^>]+>", "", html_content)
    # 解码HTML实体
    clean_text = unescape(clean_text)
    # 标准化空白字符
    clean_text = re.sub(r"\s+", " ", clean_text).strip()

    # 移除特殊字符和控制字符
    clean_text = re.sub(r"[\x00-\x1f\x7f-\x9f]", "", clean_text)
    # 移除多余的标点符号（问题所在）
    clean_text = re.sub(r"[^\w\s\.\,\!\?\-\(\)]", "", clean_text)
    # 再次标准化空白字符
    clean_text = re.sub(r"\s+", " ", clean_text).strip()

    # 截断到指定长度
    if len(clean_text) > max_length:
        # 在单词边界截断
        truncated = clean_text[:max_length]
        last_space = truncated.rfind(" ")
        if last_space > max_length * 0.8:  # 如果最后一个空格位置合理
            clean_text = truncated[:last_space] + "..."
        else:
            clean_text = truncated + "..."

    return clean_text

def test_seo_cleaning_comparison():
    """对比测试不同的SEO清理方法"""
    
    # 测试用的HTML内容
    test_html = """
    <p>Product Dimensions:</p>
    <p>Chandelier Body: 14.5" W x 11.75" H x 9.75" D<br>
    Backplate: 7" W x 0.75" D x 6.75" H<br>
    Weight: 12 lbs</p>
    
    <p>Specifications:</p>
    <p>Material: Metal, Glass<br>
    Available Finishes: Brass, Polished Nickel<br>
    Voltage: 110V-220V<br>
    Light Source: E12 Bulbs (Dimmable)<br>
    Color Temperature: Warm White 2700K<br>
    Number of Lights: 2<br>
    Certifications: UL, CE, SAA<br>
    UL Listed for Damp Locations</p>
    
    <p>Application: Suitable for use in living rooms, dining rooms, bedrooms, hotel hallways, and entryways.</p>
    
    <p>Warranty: 2 years.</p>
    
    <p>Additional Information:</p>
    <p>Compatible with sloped or vaulted ceilings (professional installation recommended).<br>
    Requires installation by a licensed electrician.<br>
    All orders ship free within the contiguous U.S. in 7-15 business days; remote areas may require additional time.</p>
    """
    
    print("=== SEO清理功能对比测试 ===")
    print("\n原始HTML内容:")
    print(test_html.strip())
    
    print("\n" + "="*80)
    print("方案1: 旧版本（有符号丢失问题）")
    old_result = clean_html_for_seo_old(test_html, max_length=300)
    print(f"长度: {len(old_result)}")
    print(f"内容: {old_result}")
    
    print("\n" + "="*80)
    print("方案2: 简化版本（只移除HTML标签，保留所有文本）")
    simple_result = clean_html_for_seo_simple(test_html, max_length=300)
    print(f"长度: {len(simple_result)}")
    print(f"内容: {simple_result}")
    
    print("\n" + "="*80)
    print("对比分析:")
    
    # 检查关键符号是否保留
    symbols_to_check = ['"', ':', 'W', 'H', 'D', 'lbs', '110V-220V', '2700K', '(', ')', ';', '-']
    
    print("\n关键符号保留情况:")
    for symbol in symbols_to_check:
        old_has = symbol in old_result
        simple_has = symbol in simple_result
        
        if simple_has:
            status = "✓"
            result = "保留"
        else:
            status = "✗"
            result = "丢失"
            
        change = ""
        if old_has != simple_has:
            change = f" (旧版: {'有' if old_has else '无'} → 新版: {'有' if simple_has else '无'})"
        
        print(f"  {status} '{symbol}': {result}{change}")
    
    print(f"\n长度对比:")
    print(f"  旧版本: {len(old_result)} 字符")
    print(f"  简化版: {len(simple_result)} 字符")
    print(f"  差异: {len(simple_result) - len(old_result)} 字符")
    
    print(f"\n优势分析:")
    print("✅ 简化版本的优势:")
    print("  - 保留所有原始文本内容和符号")
    print("  - 代码更简洁，维护性更好")
    print("  - 不会意外过滤掉重要信息")
    print("  - 更符合SEO描述的纯文本要求")
    print("  - 避免了复杂的正则表达式匹配问题")

if __name__ == "__main__":
    test_seo_cleaning_comparison()
