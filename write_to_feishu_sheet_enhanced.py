import os
import requests
import json
import logging
import traceback
from datetime import datetime

# 飞书API配置
FEISHU_APP_ID = "cli_a5a12cc72c78500c"
FEISHU_APP_SECRET = "WBP6rHyvPjeLe6qdPr84Texj1h2uc3Cw"
# 飞书电子表格ID
FEISHU_SHEET_TOKEN = "Xq4LsFeBhh9afotkOswcLr2tnqe"
# 工作表ID (可以从URL或API获取)
FEISHU_PRODUCT_SHEET_ID = "7d8ce1"
FEISHU_POLICY_SHEET_ID = "FAWERs"

# 调试模式 - 设置为True时会打印更多日志
DEBUG_MODE = True


def get_feishu_access_token():
    """获取飞书访问令牌"""
    logging.info("开始获取飞书访问令牌")
    url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
    headers = {"Content-Type": "application/json"}
    data = {"app_id": FEISHU_APP_ID, "app_secret": FEISHU_APP_SECRET}

    try:
        if DEBUG_MODE:
            logging.debug(f"飞书API请求: {url}")
            logging.debug(f"飞书API请求数据: {json.dumps(data, ensure_ascii=False)}")

        response = requests.post(
            url, headers=headers, data=json.dumps(data), timeout=30
        )
        response_json = response.json()

        if DEBUG_MODE:
            logging.debug(
                f"飞书API响应: {json.dumps(response_json, ensure_ascii=False)[:500]}"
            )

        if response.status_code == 200 and response_json.get("tenant_access_token"):
            logging.info("成功获取飞书访问令牌")
            return response_json.get("tenant_access_token")
        else:
            error_msg = f"获取飞书访问令牌失败: HTTP {response.status_code}, 响应: {response_json}"
            logging.error(error_msg)
            return None
    except Exception as e:
        error_msg = f"获取飞书访问令牌异常: {str(e)}"
        logging.error(error_msg)
        traceback.print_exc()
        return None


def verify_feishu_sheet(access_token, sheet_token):
    """验证飞书表格ID是否正确"""
    logging.info(f"验证飞书表格ID: {sheet_token}")

    url = f"https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/{sheet_token}/metainfo"
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json",
    }

    try:
        if DEBUG_MODE:
            logging.debug(f"验证表格请求: {url}")
            logging.debug(f"验证表格请求头: {headers}")

        response = requests.get(url, headers=headers, timeout=30)

        if DEBUG_MODE:
            logging.debug(f"验证表格响应状态码: {response.status_code}")
            logging.debug(f"验证表格响应内容: {response.text[:500]}")

        if response.status_code == 200:
            response_json = response.json()
            sheet_info = response_json.get("data", {}).get("spreadsheetToken", "")
            sheet_title = (
                response_json.get("data", {}).get("properties", {}).get("title", "")
            )
            logging.info(f"成功验证表格ID: {sheet_token}, 表格标题: {sheet_title}")
            return True, sheet_title
        else:
            error_msg = f"验证表格ID失败: HTTP {response.status_code}, 响应: {response.text[:200]}"
            logging.error(error_msg)
            return False, None
    except Exception as e:
        error_msg = f"验证表格ID异常: {str(e)}"
        logging.error(error_msg)
        traceback.print_exc()
        return False, None


def get_sheet_info(access_token, sheet_token):
    """获取飞书表格的工作表信息"""
    logging.info(f"获取表格工作表信息: {sheet_token}")

    # 使用metainfo API获取工作表信息
    url = f"https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/{sheet_token}/metainfo"
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json",
    }

    try:
        if DEBUG_MODE:
            logging.debug(f"获取工作表请求: {url}")
            logging.debug(f"获取工作表请求头: {headers}")

        response = requests.get(url, headers=headers, timeout=30)

        if DEBUG_MODE:
            logging.debug(f"获取工作表响应状态码: {response.status_code}")
            logging.debug(f"获取工作表响应内容: {response.text[:500]}")

        if response.status_code == 200:
            response_json = response.json()
            sheets = response_json.get("data", {}).get("sheets", [])

            if not sheets:
                # 尝试从不同的路径获取工作表信息
                sheets = (
                    response_json.get("data", {})
                    .get("properties", {})
                    .get("sheets", [])
                )

            sheet_info = []
            for sheet in sheets:
                sheet_id = sheet.get("sheetId", "")
                sheet_title = sheet.get("title", "")
                sheet_info.append({"id": sheet_id, "title": sheet_title})

            logging.info(f"成功获取工作表信息，共{len(sheet_info)}个工作表")
            return True, sheet_info
        else:
            error_msg = f"获取工作表信息失败: HTTP {response.status_code}, 响应: {response.text[:200]}"
            logging.error(error_msg)
            return False, None
    except Exception as e:
        error_msg = f"获取工作表信息异常: {str(e)}"
        logging.error(error_msg)
        traceback.print_exc()
        return False, None


def write_to_feishu_sheet_range(access_token, sheet_token, sheet_id, range_str, values):
    """写入数据到飞书表格的指定范围"""
    logging.info(f"写入数据到飞书表格范围: {range_str}")

    # 使用更新后的飞书API端点
    url = (
        f"https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/{sheet_token}/values"
    )
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {access_token}",
    }

    data = {"valueRange": {"range": range_str, "values": values}}

    try:
        # 打印详细的请求信息，用于调试
        logging.debug(f"API请求URL: {url}")
        logging.debug(f"API请求头: {headers}")
        logging.debug(f"API请求数据: 范围={range_str}, 数据行数={len(values)}")

        # 发送请求
        response = requests.put(url, headers=headers, json=data, timeout=60)

        # 记录完整的响应
        logging.debug(f"API响应状态码: {response.status_code}")
        logging.debug(f"API响应内容: {response.text[:500]}")

        if response.status_code == 200:
            logging.info(f"成功写入数据到范围: {range_str}")
            return True
        else:
            error_msg = f"写入数据失败: HTTP {response.status_code}, 响应: {response.text[:200]}"
            logging.error(error_msg)
            # 尝试解析错误响应
            try:
                error_json = response.json()
                logging.error(f"错误详情: {json.dumps(error_json, ensure_ascii=False)}")
            except:
                pass
            return False
    except Exception as e:
        error_msg = f"写入数据异常: {str(e)}"
        logging.error(error_msg)
        traceback.print_exc()
        return False


def write_to_feishu_sheet_direct(
    access_token, sheet_token, sheet_id, data, start_row=2
):
    """直接写入数据到飞书表格，使用批量写入API"""
    logging.info(f"直接写入数据到飞书表格, 工作表ID: {sheet_id}, 数据条数: {len(data)}")

    # 使用飞书批量写入API
    url = (
        f"https://open.feishu.cn/open-apis/sheets/v2/spreadsheets/{sheet_token}/values"
    )
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {access_token}",
    }

    # 准备要写入的数据
    values = []
    for row in data:
        # 确保每个单元格的值是字符串类型
        row_values = []
        for value in row:
            if value is None:
                row_values.append("")
            else:
                # 转换为字符串，不限制长度，确保完整写入
                value_str = str(value)
                # 飞书表格单元格最大支持50000个字符，但我们不做截断，让飞书API自己处理
                row_values.append(value_str)
        values.append(row_values)

    # 计算范围
    end_row = start_row + len(values) - 1
    cols_needed = max([len(row) for row in values]) if values else 1
    end_col = chr(ord("A") + cols_needed - 1)

    # 构建范围字符串
    range_str = f"{sheet_id}!A{start_row}:{end_col}{end_row}"

    # 构建请求数据
    request_data = {"valueRange": {"range": range_str, "values": values}}

    try:
        # 打印详细的请求信息，用于调试
        logging.debug(f"API请求URL: {url}")
        logging.debug(f"API请求头: {headers}")
        logging.debug(f"API请求数据范围: {range_str}, 数据行数={len(values)}")

        # 发送请求
        response = requests.put(url, headers=headers, json=request_data, timeout=60)

        # 记录完整的响应
        logging.debug(f"API响应状态码: {response.status_code}")
        logging.debug(f"API响应内容: {response.text[:500]}")

        if response.status_code == 200:
            response_json = response.json()
            if response_json.get("code", 0) == 0:
                logging.info(f"成功直接写入 {len(values)} 行数据到表格")
                return True
            else:
                error_msg = f"直接写入数据失败: 错误码 {response_json.get('code')}, 消息: {response_json.get('msg')}"
                logging.error(error_msg)
                return False
        else:
            error_msg = f"直接写入数据失败: HTTP {response.status_code}, 响应: {response.text[:200]}"
            logging.error(error_msg)
            # 尝试解析错误响应
            try:
                error_json = response.json()
                logging.error(f"错误详情: {json.dumps(error_json, ensure_ascii=False)}")
            except:
                pass
            return False
    except Exception as e:
        error_msg = f"直接写入数据异常: {str(e)}"
        logging.error(error_msg)
        traceback.print_exc()
        return False


def write_to_feishu_sheet(products_data, policies_data=None):
    """
    将产品分析结果写入飞书表格，支持更丰富的数据结构

    参数:
        products_data (list): 产品分析数据列表
        policies_data (list, optional): 政策分析数据列表，默认为None，不再使用

    返回:
        bool: 是否成功写入数据
    """
    print("\n正在将产品分析结果写入飞书表格...")
    logging.info("开始写入飞书表格")

    # 确保policies_data不为None
    if policies_data is None:
        policies_data = []

    try:
        # 获取访问令牌
        access_token = get_feishu_access_token()
        if not access_token:
            error_msg = "获取飞书访问令牌失败，无法写入表格"
            logging.error(error_msg)
            print(f"× {error_msg}")
            return False

        # 验证表格ID
        print("验证飞书表格ID...")
        sheet_valid, sheet_title = verify_feishu_sheet(access_token, FEISHU_SHEET_TOKEN)
        if not sheet_valid:
            error_msg = f"飞书表格ID无效: {FEISHU_SHEET_TOKEN}"
            logging.error(error_msg)
            print(f"× {error_msg}")
            return False
        else:
            print(f"✓ 成功验证飞书表格: {sheet_title}")

        # 获取工作表信息
        print("获取工作表信息...")
        sheets_valid, sheets_info = get_sheet_info(access_token, FEISHU_SHEET_TOKEN)
        if not sheets_valid:
            error_msg = "无法获取工作表信息"
            logging.error(error_msg)
            print(f"× {error_msg}")
            return False
        else:
            # 打印工作表信息
            print(f"✓ 成功获取工作表信息，共{len(sheets_info)}个工作表:")
            for sheet in sheets_info:
                print(f"  - ID: {sheet['id']}, 标题: {sheet['title']}")

            # 验证产品表和政策表ID
            product_sheet_found = False
            policy_sheet_found = False

            for sheet in sheets_info:
                if sheet["id"] == FEISHU_PRODUCT_SHEET_ID:
                    product_sheet_found = True
                    print(f"✓ 找到产品表: {sheet['title']} (ID: {sheet['id']})")
                if sheet["id"] == FEISHU_POLICY_SHEET_ID:
                    policy_sheet_found = True
                    print(f"✓ 找到政策表: {sheet['title']} (ID: {sheet['id']})")

            if not product_sheet_found:
                print(f"× 警告: 未找到产品表ID: {FEISHU_PRODUCT_SHEET_ID}")
                print("  请检查产品表ID是否正确，或者从以上工作表中选择正确的ID")

            if not policy_sheet_found:
                print(f"× 警告: 未找到政策表ID: {FEISHU_POLICY_SHEET_ID}")
                print("  请检查政策表ID是否正确，或者从以上工作表中选择正确的ID")

        # 准备产品数据，使用更丰富的字段
        product_values = []
        for product in products_data:
            # 构建更详细的产品数据行
            product_values.append(
                [
                    product.get("product_id", ""),
                    product.get("title", ""),
                    product.get("description", ""),
                    product.get("link", ""),
                    product.get("price", ""),
                    str(product.get("inventory", "")),
                    product.get("product_type", ""),
                    product.get("special_categories", ""),
                    product.get("gmc_risk", ""),
                    (
                        product.get("issues_and_fixes", "无具体问题")
                        if "issues_and_fixes" in product
                        else product.get("issues_summary", "无具体问题")
                    ),
                ]
            )

        # 准备政策数据，使用更丰富的字段
        policy_values = []
        for policy in policies_data:
            policy_values.append(
                [
                    policy.get("title", ""),
                    policy.get("link", ""),
                    policy.get("policy_status", "未知"),
                    policy.get("required_by_gmc", "未知"),
                    policy.get("gmc_risk", ""),
                    policy.get("missing_elements_text", ""),
                    policy.get("fix_suggestion", ""),
                    policy.get("gmc_requirement", ""),
                ]
            )

        # 尝试使用直接写入方法
        success = True

        # 写入产品数据
        if product_values:
            # 准备表头
            product_headers = [
                "产品ID",
                "产品标题",
                "产品描述",
                "产品链接",
                "价格",
                "库存",
                "产品类型",
                "特殊类别",
                "GMC风险",
                "问题及修复",
            ]

            # 将表头添加到数据中
            all_product_data = [product_headers] + product_values

            # 直接写入产品数据（包括表头）
            product_result = write_to_feishu_sheet_direct(
                access_token,
                FEISHU_SHEET_TOKEN,
                FEISHU_PRODUCT_SHEET_ID,
                all_product_data,
                start_row=1,  # 从第1行开始（包括表头）
            )

            if not product_result:
                logging.warning("直接写入产品数据失败，尝试使用常规方法")
                success = False

                # 尝试使用常规方法
                try:
                    # 清空产品表
                    clear_product_range = f"A2:J1000"  # 假设最多1000行数据
                    clear_product_result = write_to_feishu_sheet_range(
                        access_token,
                        FEISHU_SHEET_TOKEN,
                        FEISHU_PRODUCT_SHEET_ID,
                        clear_product_range,
                        [[""] * 10] * 999,  # 999行空数据
                    )
                    logging.info("清空产品表完成")

                    # 写入表头
                    write_to_feishu_sheet_range(
                        access_token,
                        FEISHU_SHEET_TOKEN,
                        FEISHU_PRODUCT_SHEET_ID,
                        "A1:J1",
                        [product_headers],
                    )
                    logging.info("更新产品表表头完成")

                    # 写入数据
                    product_range = f"A2:J{len(product_values) + 1}"
                    product_result = write_to_feishu_sheet_range(
                        access_token,
                        FEISHU_SHEET_TOKEN,
                        FEISHU_PRODUCT_SHEET_ID,
                        product_range,
                        product_values,
                    )

                    if product_result:
                        logging.info(f"成功写入{len(product_values)}条产品数据")
                        print(f"✓ 成功写入{len(product_values)}条产品数据")
                    else:
                        logging.error("写入产品数据失败")
                        print("× 写入产品数据失败")
                        success = False
                except Exception as e:
                    logging.error(f"常规方法写入产品数据失败: {str(e)}")
                    print(f"× 常规方法写入产品数据失败: {str(e)}")
                    success = False
            else:
                logging.info(f"成功直接写入{len(product_values)}条产品数据")
                print(f"✓ 成功直接写入{len(product_values)}条产品数据")

        # 不再处理政策数据
        if policy_values:
            logging.info("跳过政策数据处理，仅处理产品数据")
            print("跳过政策数据处理，仅处理产品数据")

        return success
    except Exception as e:
        logging.error(f"写入飞书表格失败: {str(e)}")
        traceback.print_exc()
        print(f"× 写入飞书表格失败: {str(e)}")
        return False
