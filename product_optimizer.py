import requests
import json
import time
import threading
import concurrent.futures
import re
from html import unescape


class ProductOptimizer:
    """
    产品信息优化器，使用AI优化产品标题、描述和Handle，符合谷歌GMC政策规范
    """

    def __init__(self, config, rate_limiter, print_lock):
        """
        初始化产品优化器

        参数:
            config (dict): 配置信息
            rate_limiter: 速率限制器
            print_lock: 打印锁
        """
        self.config = config
        self.rate_limiter = rate_limiter
        self.print_lock = print_lock

        # 初始化AI模型配置
        self.active_model = config.get("active_model", "qwen")
        self.model_config = config.get("models", {}).get(self.active_model, {})

        with self.print_lock:
            print(f"✓ 产品优化器初始化完成，使用AI模型: {self.active_model}")

    def _call_ai_model(self, prompt, max_tokens=2048):
        """
        调用AI模型进行文本优化

        参数:
            prompt (str): 提示词
            max_tokens (int): 最大token数

        返回:
            str: AI模型的响应
        """
        try:
            api_url = self.model_config.get("api_url")
            api_key = self.model_config.get("api_key")
            model_id = self.model_config.get("model_id")
            temperature = self.model_config.get("temperature", 0.1)

            if not all([api_url, api_key, model_id]):
                raise ValueError("AI模型配置不完整")

            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }

            # 根据不同的API构建请求
            if "dashscope.aliyuncs.com" in api_url:
                # 阿里云DashScope API
                data = {
                    "model": model_id,
                    "input": {
                        "messages": [
                            {
                                "role": "user",
                                "content": prompt
                            }
                        ]
                    },
                    "parameters": {
                        "max_tokens": max_tokens,
                        "temperature": temperature
                    }
                }
                response = requests.post(f"{api_url}/v1/services/aigc/text-generation/generation",
                                       headers=headers, json=data, timeout=60)
            elif "generativelanguage.googleapis.com" in api_url:
                # Google Gemini API
                data = {
                    "contents": [
                        {
                            "parts": [
                                {
                                    "text": prompt
                                }
                            ]
                        }
                    ],
                    "generationConfig": {
                        "maxOutputTokens": max_tokens,
                        "temperature": temperature
                    }
                }
                response = requests.post(f"{api_url}/v1beta/models/{model_id}:generateContent?key={api_key}",
                                       headers={"Content-Type": "application/json"}, json=data, timeout=60)
            else:
                # OpenAI兼容API (SiliconFlow等)
                data = {
                    "model": model_id,
                    "messages": [
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ],
                    "max_tokens": max_tokens,
                    "temperature": temperature
                }
                response = requests.post(f"{api_url}/v1/chat/completions",
                                       headers=headers, json=data, timeout=60)

            if response.status_code == 200:
                result = response.json()

                # 解析不同API的响应格式
                if "dashscope.aliyuncs.com" in api_url:
                    return result.get("output", {}).get("text", "")
                elif "generativelanguage.googleapis.com" in api_url:
                    candidates = result.get("candidates", [])
                    if candidates:
                        return candidates[0].get("content", {}).get("parts", [{}])[0].get("text", "")
                else:
                    choices = result.get("choices", [])
                    if choices:
                        return choices[0].get("message", {}).get("content", "")

                return ""
            else:
                with self.print_lock:
                    print(f"AI模型调用失败: {response.status_code} - {response.text}")
                return ""

        except Exception as e:
            with self.print_lock:
                print(f"AI模型调用异常: {e}")
            return ""



    def _extract_image_references(self, html_content):
        """
        提取HTML内容中的图片引用信息

        参数:
            html_content (str): HTML内容

        返回:
            list: 图片引用信息列表
        """
        if not html_content:
            return []

        image_refs = []

        # 匹配img标签
        img_pattern = r'<img[^>]*src=["\']([^"\']*)["\'][^>]*>'
        img_matches = re.finditer(img_pattern, html_content, re.IGNORECASE)

        for match in img_matches:
            img_tag = match.group(0)
            img_src = match.group(1)

            # 获取图片前后的文本上下文
            start_pos = max(0, match.start() - 200)
            end_pos = min(len(html_content), match.end() + 200)
            context = html_content[start_pos:end_pos]

            # 清理上下文文本
            context_text = re.sub(r'<[^>]+>', ' ', context)
            context_text = re.sub(r'\s+', ' ', context_text).strip()

            image_refs.append({
                'tag': img_tag,
                'src': img_src,
                'context': context_text,
                'position': match.start()
            })

        return image_refs

    def _preserve_image_context(self, original_html, optimized_text, image_refs):
        """
        在优化后的文本中保留图片上下文

        参数:
            original_html (str): 原始HTML
            optimized_text (str): 优化后的文本
            image_refs (list): 图片引用信息

        返回:
            str: 保留图片上下文的优化HTML
        """
        if not image_refs:
            return optimized_text

        # 如果优化后的文本是纯文本，需要转换为HTML格式
        if not re.search(r'<[^>]+>', optimized_text):
            # 将纯文本转换为段落
            paragraphs = optimized_text.split('\n\n')
            optimized_text = '\n'.join([f'<p>{p.strip()}</p>' for p in paragraphs if p.strip()])

        # 尝试在合适的位置插入图片
        for img_ref in image_refs:
            context_keywords = img_ref['context'].lower()

            # 查找与图片上下文相关的位置
            if any(keyword in context_keywords for keyword in ['size', 'dimension', 'measurement', '尺寸', '大小']):
                # 尺寸相关的图片，尝试在描述尺寸的段落后插入
                size_pattern = r'(<p>[^<]*(?:size|dimension|measurement|尺寸|大小)[^<]*</p>)'
                match = re.search(size_pattern, optimized_text, re.IGNORECASE)
                if match:
                    insert_pos = match.end()
                    optimized_text = (optimized_text[:insert_pos] +
                                    f'\n{img_ref["tag"]}\n' +
                                    optimized_text[insert_pos:])
                    continue

            # 如果没有找到特定位置，在第一段后插入
            first_p_match = re.search(r'</p>', optimized_text)
            if first_p_match:
                insert_pos = first_p_match.end()
                optimized_text = (optimized_text[:insert_pos] +
                                f'\n{img_ref["tag"]}\n' +
                                optimized_text[insert_pos:])

        return optimized_text

    def optimize_product_handle(self, original_handle, optimized_title, product_type="", brand=""):
        """
        使用AI优化产品Handle，使其更利于SEO

        参数:
            original_handle (str): 原始Handle
            optimized_title (str): 优化后的标题
            product_type (str): 产品类型
            brand (str): 品牌名称

        返回:
            str: 优化后的Handle
        """
        if not optimized_title:
            return original_handle or ""

        # 构建Handle优化提示词
        prompt = f"""请优化以下产品Handle，使其更利于SEO：

原始Handle：{original_handle}
优化后的产品标题：{optimized_title}
品牌：{brand}

优化要求：
1. 必须输出英文，禁止出现中文、日文、韩文等其他语言
2. 不要包含产品类型信息，专注于产品特征
3. 使用连字符(-)分隔单词
4. 长度控制在50字符以内
5. 只使用小写字母、数字和连字符
6. 利于SEO，包含关键词
7. 简洁明了，易于记忆
8. 符合Shopify Handle规范

请直接返回优化后的Handle，不要包含其他解释文字："""

        optimized_handle = self._call_ai_model(prompt, max_tokens=100)

        if optimized_handle:
            # 清理AI返回的内容
            optimized_handle = optimized_handle.strip().strip('"\'').lower()

            # 确保只包含允许的字符
            optimized_handle = re.sub(r'[^a-z0-9\s\-]', '', optimized_handle)

            # 将空格替换为连字符
            optimized_handle = re.sub(r'\s+', '-', optimized_handle)

            # 移除多余的连字符
            optimized_handle = re.sub(r'-+', '-', optimized_handle)

            # 移除开头和结尾的连字符
            optimized_handle = optimized_handle.strip('-')

            # 限制长度
            if len(optimized_handle) > 50:
                optimized_handle = optimized_handle[:50].rstrip('-')

            # 如果优化后的Handle为空或太短，回退到基础生成方法
            if len(optimized_handle) < 3:
                return self._generate_handle_from_title(optimized_title)

            return optimized_handle

        # 如果AI优化失败，回退到基础生成方法
        return self._generate_handle_from_title(optimized_title)

    def _generate_handle_from_title(self, title):
        """
        从标题生成符合Shopify规范的handle（备用方法）

        参数:
            title (str): 产品标题

        返回:
            str: 生成的handle
        """
        if not title:
            return ""

        # 转换为小写
        handle = title.lower()

        # 移除特殊字符，只保留字母、数字、空格和连字符
        handle = re.sub(r'[^a-z0-9\s\-]', '', handle)

        # 将空格替换为连字符
        handle = re.sub(r'\s+', '-', handle)

        # 移除多余的连字符
        handle = re.sub(r'-+', '-', handle)

        # 移除开头和结尾的连字符
        handle = handle.strip('-')

        # 限制长度（Shopify handle最大长度为255字符）
        if len(handle) > 50:  # 保守一点，限制为50字符
            handle = handle[:50].rstrip('-')

        return handle

    def _clean_handle(self, handle):
        """
        清理和验证Handle

        参数:
            handle (str): 原始Handle

        返回:
            str: 清理后的Handle
        """
        if not handle:
            return ""

        # 转换为小写
        handle = handle.lower()

        # 移除特殊字符，只保留字母、数字、空格和连字符
        handle = re.sub(r'[^a-z0-9\s\-]', '', handle)

        # 将空格替换为连字符
        handle = re.sub(r'\s+', '-', handle)

        # 移除多余的连字符
        handle = re.sub(r'-+', '-', handle)

        # 移除开头和结尾的连字符
        handle = handle.strip('-')

        # 限制长度
        if len(handle) > 50:
            handle = handle[:50].rstrip('-')

        return handle

    def _clean_html_for_display(self, html_content):
        """
        清理HTML内容用于显示

        参数:
            html_content (str): HTML内容

        返回:
            str: 清理后的文本
        """
        if not html_content:
            return ""

        # 移除HTML标签
        clean_text = re.sub(r'<[^>]+>', '', html_content)
        # 解码HTML实体
        clean_text = unescape(clean_text)
        # 标准化空白字符
        clean_text = re.sub(r'\s+', ' ', clean_text).strip()

        return clean_text

    def optimize_product_title(self, title, product_type="", brand=""):
        """
        优化产品标题

        参数:
            title (str): 原始标题
            product_type (str): 产品类型
            brand (str): 品牌名称

        返回:
            str: 优化后的标题
        """
        if not title:
            return ""

        # 构建优化提示词
        prompt = f"""请优化以下产品标题，使其符合谷歌GMC（Google Merchant Center）政策规范：

原始标题：{title}
产品类型：{product_type}
品牌：{brand}

优化要求：
1. 标题应准确描述产品，不得误导消费者
2. 避免使用夸大或虚假的宣传词汇（如"最好的"、"神奇的"、"保证"等）
3. 移除过度的大写字母和感叹号
4. 包含关键的产品特征（如尺寸、颜色、材质等）
5. 使用清晰、专业的语言
6. 符合谷歌购物广告的标题规范

请直接返回优化后的标题，不要包含其他解释文字："""

        optimized_title = self._call_ai_model(prompt, max_tokens=200)

        if optimized_title:
            # 清理可能的引号和多余空格
            optimized_title = optimized_title.strip().strip('"\'')
            # 限制长度
            if len(optimized_title) > 150:
                optimized_title = optimized_title[:147] + "..."
            return optimized_title

        return title  # 如果优化失败，返回原标题

    def optimize_product_description(self, description, title="", handle=""):
        """
        优化产品描述

        参数:
            description (str): 原始描述（HTML格式）
            title (str): 产品标题
            handle (str): 产品Handle

        返回:
            str: 优化后的描述（HTML格式）
        """
        if not description:
            return ""

        # 提取图片引用
        image_refs = self._extract_image_references(description)

        # 获取纯文本描述用于分析
        clean_description = self._clean_html_for_display(description)

        # 构建优化提示词
        prompt = f"""请优化以下产品描述，使其符合谷歌GMC（Google Merchant Center）政策规范：

产品标题：{title}
Handle：{handle}
原始描述：
{clean_description}

优化要求：
1. 描述必须准确、真实，不得包含误导性信息
2. 避免夸大宣传和虚假承诺（如"最好的"、"神奇的"、"保证"等）
3. 移除销售压力用语（如"立即购买"、"限时优惠"等）
4. 包含产品的关键特征和规格
5. 使用清晰、专业的语言
6. 结构清晰，便于阅读
7. 符合谷歌购物广告的内容政策
8. 保持HTML格式，使用<p>、<ul>、<li>等标签
9. 如果原文提到产品尺寸、规格等重要信息，请保留

请返回优化后的HTML格式描述："""

        optimized_description = self._call_ai_model(prompt, max_tokens=1500)

        if optimized_description:
            # 确保返回的是HTML格式
            if not re.search(r'<[^>]+>', optimized_description):
                # 如果返回的是纯文本，转换为HTML
                paragraphs = optimized_description.split('\n\n')
                optimized_description = '\n'.join([f'<p>{p.strip()}</p>' for p in paragraphs if p.strip()])

            # 保留图片上下文
            optimized_description = self._preserve_image_context(description, optimized_description, image_refs)

            return optimized_description

        return description  # 如果优化失败，返回原描述

    def optimize_product_comprehensive(self, title, description, original_handle="", brand=""):
        """
        综合优化产品信息（标题、描述、Handle）

        参数:
            title (str): 原始标题
            description (str): 原始描述（HTML格式）
            original_handle (str): 原始Handle
            brand (str): 品牌名称

        返回:
            dict: 包含优化后的标题、描述和handle
        """
        if not title:
            return {"title": "", "description": description, "handle": original_handle}

        # 提取图片引用
        image_refs = self._extract_image_references(description) if description else []

        # 获取纯文本描述用于分析
        clean_description = self._clean_html_for_display(description) if description else ""

        # 构建简化的综合优化提示词
        prompt = f"""产品原标题：{title}
产品原描述：{clean_description}
品牌：{brand}

您是一名SEO专家，请根据以上原标题、描述内容、品牌，优化生成新的标题和描述，使其符合谷歌GMC（Google Merchant Center）政策规范：

优化要求：
1. 准确描述产品，不得误导消费者
2. 避免使用夸大或虚假的宣传词汇（如"最好的"、"神奇的"、"保证"等）
3. 移除过度的大写字母和感叹号
4. 使用清晰、专业的语言
5. 符合谷歌购物广告的标题规范

请直接返回优化后的JSON格式，不要包含其他解释文字：

JSON格式示例：
{{
"新标题 (English)": "New Title，10-70个字符",
"产品描述(English)": "优化后的描述",
"SEO产品handle": "产品handle"
}}"""

        response = self._call_ai_model(prompt, max_tokens=2000)

        if response:
            try:
                # 尝试解析JSON响应
                result = json.loads(response.strip())

                # 提取优化后的内容，支持中英文键名
                optimized_title = (result.get("新标题 (English)") or
                                 result.get("title") or
                                 title).strip().strip('"\'')

                optimized_description = (result.get("产品描述(English)") or
                                       result.get("description") or
                                       description)

                optimized_handle = (result.get("SEO产品handle") or
                                  result.get("handle") or
                                  "").strip().strip('"\'')

                # 限制标题长度
                if len(optimized_title) > 70:
                    optimized_title = optimized_title[:67] + "..."

                # 确保描述是HTML格式
                if optimized_description and not re.search(r'<[^>]+>', optimized_description):
                    paragraphs = optimized_description.split('\n\n')
                    optimized_description = '\n'.join([f'<p>{p.strip()}</p>' for p in paragraphs if p.strip()])

                # 保留图片上下文
                if image_refs and optimized_description:
                    optimized_description = self._preserve_image_context(description, optimized_description, image_refs)

                # 清理和验证Handle
                if optimized_handle:
                    optimized_handle = self._clean_handle(optimized_handle)

                # 如果Handle为空或无效，使用标题生成
                if not optimized_handle or len(optimized_handle) < 3:
                    optimized_handle = self._generate_handle_from_title(optimized_title)

                return {
                    "title": optimized_title,
                    "description": optimized_description,
                    "handle": optimized_handle
                }

            except json.JSONDecodeError:
                # 如果JSON解析失败，回退到单独优化
                with self.print_lock:
                    print("AI返回格式错误，使用单独优化方式")

                optimized_title = self.optimize_product_title(title, product_type, brand)
                optimized_description = self.optimize_product_description(description, optimized_title, product_type)
                optimized_handle = self._generate_handle_from_title(optimized_title)

                return {
                    "title": optimized_title,
                    "description": optimized_description,
                    "handle": optimized_handle
                }

        # 如果AI调用失败，返回原始数据
        return {
            "title": title,
            "description": description,
            "handle": original_handle or self._generate_handle_from_title(title)
        }

    def optimize_single_product(self, product, product_index=None, total_products=None):
        """
        优化单个产品的信息

        参数:
            product (dict): 产品信息
            product_index (int): 产品索引
            total_products (int): 总产品数

        返回:
            tuple: (result_type, product_info)
        """
        try:
            product_id = product["id"]

            # 获取产品详细信息
            shop_name = self.config["shopify"]["shop_name"]
            url = f"https://{shop_name}.myshopify.com/admin/api/2023-04/products/{product_id}.json"
            headers = self._get_auth_headers()

            self.rate_limiter.wait_if_needed()
            resp = requests.get(url, headers=headers, verify=False, timeout=10)

            if resp.status_code != 200:
                with self.print_lock:
                    print(f"获取产品{product_id}详情失败: {resp.status_code} - {resp.text}")
                return "failed", {
                    "id": product_id,
                    "title": product.get("title", f"ID: {product_id}"),
                }

            product_data = resp.json().get("product", {})
            if not product_data:
                with self.print_lock:
                    print(f"获取产品{product_id}详情数据格式错误")
                return "failed", {
                    "id": product_id,
                    "title": product.get("title", f"ID: {product_id}"),
                }

            original_title = product_data.get("title", "")
            original_description = product_data.get("body_html", "")
            original_handle = product_data.get("handle", "")
            product_type = product_data.get("product_type", "")
            vendor = product_data.get("vendor", "")

            if not original_title:
                with self.print_lock:
                    print(f"产品{product_id}没有标题，跳过")
                return "skipped", {"id": product_id, "title": "无标题"}

            # 显示原始信息
            with self.print_lock:
                print(f"\n正在优化产品 {product_id}: {original_title}")
                if product_index is not None and total_products is not None:
                    print(f"进度: {product_index+1}/{total_products}")
                print(f"原始标题: {original_title}")
                print(f"原始描述: {self._clean_html_for_display(original_description)[:200]}...")

            # 使用综合优化方法一次性优化所有字段
            optimization_result = self.optimize_product_comprehensive(
                original_title,
                original_description,
                original_handle,
                vendor
            )

            optimized_title = optimization_result["title"]
            optimized_description = optimization_result["description"]
            optimized_handle = optimization_result["handle"]

            # 检查是否有变化
            title_changed = optimized_title != original_title
            description_changed = optimized_description != original_description
            handle_changed = optimized_handle != original_handle and optimized_handle

            if not (title_changed or description_changed or handle_changed):
                with self.print_lock:
                    print(f"产品{product_id}无需优化")
                return "skipped", {"id": product_id, "title": original_title}

            # 显示优化结果
            with self.print_lock:
                if title_changed:
                    print(f"✓ 标题已优化: {optimized_title}")
                if description_changed:
                    print(f"✓ 描述已优化: {self._clean_html_for_display(optimized_description)[:200]}...")
                if handle_changed:
                    print(f"✓ Handle已优化: {optimized_handle}")

            # 更新产品
            success = self._update_product_info(
                product_id,
                optimized_title if title_changed else None,
                optimized_description if description_changed else None,
                optimized_handle if handle_changed else None
            )

            if success:
                # 生成产品链接
                custom_domain = self.config["shopify"].get("custom_domain", "")
                shop_name = self.config["shopify"]["shop_name"]
                final_handle = optimized_handle if handle_changed else original_handle
                product_link = (
                    f"https://{custom_domain}/products/{final_handle}"
                    if custom_domain
                    else f"https://{shop_name}.myshopify.com/products/{final_handle}"
                )

                with self.print_lock:
                    print(f"✓ 产品 {optimized_title}（ID: {product_id}）优化成功")
                    print(f"  产品链接: {product_link}")

                return "updated", {
                    "id": product_id,
                    "title": optimized_title,
                    "link": product_link,
                }
            else:
                with self.print_lock:
                    print(f"× 产品 {original_title}（ID: {product_id}）优化失败")
                return "failed", {"id": product_id, "title": original_title}

        except Exception as e:
            with self.print_lock:
                print(f"处理产品优化时发生错误: {str(e)}")
            return "failed", {
                "id": product.get("id", "unknown"),
                "title": product.get("title", "unknown"),
            }

    def _get_auth_headers(self):
        """生成Basic认证头"""
        import base64

        api_key = self.config["shopify"]["api_key"]
        api_password = self.config["shopify"]["api_password"]
        auth_str = f"{api_key}:{api_password}"
        auth_bytes = auth_str.encode("ascii")
        auth_b64_bytes = base64.b64encode(auth_bytes)
        auth_b64_str = auth_b64_bytes.decode("ascii")
        return {
            "Content-Type": "application/json",
            "Authorization": f"Basic {auth_b64_str}",
        }

    def _update_product_info(self, product_id, title=None, description=None, handle=None):
        """
        更新产品信息

        参数:
            product_id (int): 产品ID
            title (str, optional): 新标题
            description (str, optional): 新描述
            handle (str, optional): 新handle

        返回:
            bool: 更新是否成功
        """
        try:
            shop_name = self.config["shopify"]["shop_name"]
            url = f"https://{shop_name}.myshopify.com/admin/api/2023-04/products/{product_id}.json"
            headers = self._get_auth_headers()

            # 构建更新数据
            update_data = {"product": {"id": product_id}}

            if title:
                update_data["product"]["title"] = title
            if description:
                update_data["product"]["body_html"] = description
            if handle:
                update_data["product"]["handle"] = handle

            self.rate_limiter.wait_if_needed()
            resp = requests.put(url, headers=headers, json=update_data, verify=False, timeout=10)

            if resp.status_code == 200:
                return True
            else:
                with self.print_lock:
                    print(f"更新产品{product_id}失败: {resp.status_code} - {resp.text}")
                return False

        except Exception as e:
            with self.print_lock:
                print(f"更新产品{product_id}异常: {e}")
            return False


def optimize_products_for_gmc(products, config, rate_limiter, print_lock, result_collector, max_workers=1):
    """
    批量优化产品信息以符合GMC政策规范

    参数:
        products (list): 产品列表
        config (dict): 配置信息
        rate_limiter: 速率限制器
        print_lock: 打印锁
        result_collector: 结果收集器
        max_workers (int): 最大线程数
    """
    # 创建产品优化器
    optimizer = ProductOptimizer(config, rate_limiter, print_lock)

    total_products = len(products)
    print(f"开始优化 {total_products} 个产品的信息...")

    start_time = time.time()

    if max_workers > 1:
        # 多线程模式
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_index = {}
            for i, product in enumerate(products):
                future = executor.submit(
                    optimizer.optimize_single_product, product, i, total_products
                )
                future_to_index[future] = i

            # 处理完成的任务
            for future in concurrent.futures.as_completed(future_to_index):
                product_index = future_to_index[future]
                try:
                    result_type, product_info = future.result()
                    result_collector.add_result(result_type, product_info)

                    # 定期显示进度
                    stats = result_collector.get_stats()
                    if (stats["total"] % 5 == 0) or (stats["total"] == total_products):
                        elapsed = time.time() - start_time
                        with print_lock:
                            print(
                                f"\n当前进度: {stats['total']}/{total_products} "
                                f"(成功: {stats['updated']}, 跳过: {stats['skipped']}, "
                                f"失败: {stats['failed']}) - 耗时: {elapsed:.1f}秒"
                            )
                except Exception as e:
                    with print_lock:
                        print(f"处理产品 {product_index+1}/{total_products} 时发生错误: {str(e)}")
                    result_collector.add_result("failed")
    else:
        # 单线程模式
        for i, product in enumerate(products):
            result_type, product_info = optimizer.optimize_single_product(product, i, total_products)
            result_collector.add_result(result_type, product_info)

            # 定期显示进度
            if (i + 1) % 5 == 0 or (i + 1) == total_products:
                stats = result_collector.get_stats()
                elapsed = time.time() - start_time
                with print_lock:
                    print(
                        f"\n当前进度: {stats['total']}/{total_products} "
                        f"(成功: {stats['updated']}, 跳过: {stats['skipped']}, "
                        f"失败: {stats['failed']}) - 耗时: {elapsed:.1f}秒"
                    )

    # 显示最终统计
    final_stats = result_collector.get_stats()
    total_time = time.time() - start_time

    with print_lock:
        print("\n" + "="*60)
        print("产品优化完成！")
        print("="*60)
        print(f"总处理产品数: {final_stats['total']}")
        print(f"成功优化: {final_stats['updated']}")
        print(f"跳过处理: {final_stats['skipped']}")
        print(f"处理失败: {final_stats['failed']}")
        print(f"总耗时: {total_time:.1f}秒")
        print(f"平均处理时间: {total_time/total_products:.1f}秒/产品")
        print("="*60)