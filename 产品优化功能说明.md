# 产品信息AI优化功能说明

## 功能概述

本功能通过AI模型自动优化Shopify产品信息，使其符合谷歌GMC（Google Merchant Center）政策规范。优化内容包括：

- **产品标题**：确保准确描述产品，符合GMC标题规范
- **产品描述**：优化内容结构，保留图片引用，符合政策要求
- **产品Handle**：基于优化后的标题生成SEO友好的URL

## 主要特性

### 1. 智能政策优化
- 基于AI模型内置的GMC政策知识进行优化
- 自动识别和移除违规内容
- 确保产品信息符合谷歌购物广告规范

### 2. 图片保留机制
- 自动识别产品描述中的图片标签
- 分析图片上下文（如尺寸图片、产品展示图等）
- 在优化后的描述中智能保留图片位置

### 3. 多模型支持
- 支持多种AI模型：Qwen、DeepSeek、Gemini等
- 可在config.json中配置不同的API提供商
- 自动处理不同API的请求格式

### 4. 批量处理
- 支持单个产品或批量产品优化
- 多线程并行处理，提高效率
- 实时进度显示和错误处理

## 使用方法

### 1. 配置准备

#### AI模型配置
在`config.json`中配置AI模型信息：

```json
{
  "models": {
    "qwen": {
      "name": "Qwen3-235B-A22B",
      "api_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
      "api_key": "your-api-key",
      "model_id": "qwen3-235b-a22b",
      "max_tokens": 4096,
      "temperature": 0.1
    }
  },
  "active_model": "qwen"
}
```

#### AI模型准备
确保您的AI模型API有足够的配额，并且网络连接稳定。

### 2. 运行优化

#### 方法一：通过主脚本
```bash
python 批量修改产品信息.py
```

选择操作模式：
1. 选择"1. 批量修改所有产品"或"2. 只修改单个产品"
2. 选择"5. AI优化产品信息（标题、描述、Handle）符合GMC政策规范"
3. 确认开始优化
4. 设置并行线程数（建议1-5个线程）

#### 方法二：直接调用模块
```python
import product_optimizer
from 批量修改产品信息 import CONFIG, shopify_rate_limiter, print_lock, ResultCollector

# 获取产品列表
products = [{"id": 12345}, {"id": 67890}]  # 产品ID列表

# 创建结果收集器
result_collector = ResultCollector()

# 执行优化
product_optimizer.optimize_products_for_gmc(
    products=products,
    config=CONFIG,
    rate_limiter=shopify_rate_limiter,
    print_lock=print_lock,
    result_collector=result_collector,
    max_workers=2
)
```

### 3. 测试功能

运行测试脚本验证功能：

```bash
python test_product_optimizer.py
```

## 优化示例

### 标题优化示例

**优化前：**
```
Amazing Super Best Quality LED Light Bulb 100W Bright White SALE!!!
```

**优化后：**
```
LED Light Bulb 100W Equivalent Daylight White Energy Efficient
```

### 描述优化示例

**优化前：**
```html
<p>This is the BEST LED light bulb you will EVER find! GUARANTEED to last FOREVER!</p>
<p>Dimensions: 4.5" x 2.3"</p>
<img src="bulb-size.jpg" alt="size">
<p>Buy now and save money!</p>
```

**优化后：**
```html
<p>Energy-efficient LED light bulb with 100W equivalent brightness in daylight white.</p>
<p>Product specifications: 4.5" height x 2.3" diameter, 25,000-hour lifespan.</p>
<img src="bulb-size.jpg" alt="size">
<p>Suitable for residential and commercial lighting applications.</p>
```

## 注意事项

### 1. API配额管理
- AI模型调用会消耗API配额
- 建议先用少量产品测试
- 监控API使用情况，避免超出限制

### 2. 网络连接
- 确保网络连接稳定
- AI模型调用可能需要较长时间
- 建议在网络条件良好时进行批量优化

### 3. 备份数据
- 优化前建议备份产品数据
- 可以先在测试环境验证效果
- 重要产品建议手动检查优化结果

### 4. 政策更新
- GMC政策可能会更新
- 定期更新政策文档到ChromaDB
- 关注谷歌官方政策变更通知

## 故障排除

### 1. 网络连接问题
```
AI模型调用异常: [网络错误]
```
**解决方案：**
- 检查网络连接是否稳定
- 确认API服务器是否可访问
- 尝试重新运行优化

### 2. AI模型调用失败
```
AI模型调用失败: 401 - Unauthorized
```
**解决方案：**
- 检查API密钥是否正确
- 确认API配额是否充足
- 验证模型ID是否正确

### 3. 产品更新失败
```
更新产品12345失败: 422 - Handle has already been taken
```
**解决方案：**
- Handle冲突，系统会自动处理
- 检查Shopify API权限
- 确认产品ID是否存在

## 技术架构

```
用户输入
    ↓
批量修改产品信息.py (主脚本)
    ↓
product_optimizer.py (优化模块)
    ↓
┌─────────────────┬─────────────────┐
│   AI模型API     │   Shopify API   │
│   (内容优化)     │   (产品更新)     │
└─────────────────┴─────────────────┘
```

## 更新日志

### v1.0.0 (2025-01-28)
- 初始版本发布
- 支持标题、描述、Handle优化
- 基于AI模型内置知识进行GMC政策优化
- 支持多种AI模型（Qwen、DeepSeek、Gemini等）
- 智能图片保留机制
- 批量处理功能
- 综合优化模式，一次性优化所有字段
