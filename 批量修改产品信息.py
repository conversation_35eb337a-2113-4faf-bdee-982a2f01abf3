import requests
import json
import os
import time
import traceback
from datetime import datetime
import base64
import logging
import urllib3
import threading
import concurrent.futures
from queue import Queue
import re
import seo_sync
import media_alt_sync
import product_optimizer
from html_text_replacer import smart_text_replace


# 加载配置文件
def load_config():
    """加载配置文件"""
    try:
        with open("config.json", "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception as e:
        logging.error(f"加载配置文件失败: {str(e)}")
        # 返回默认配置
        return {
            "shopify": {
                "shop_name": "9uii4g-sf",
                "api_key": "7413850cc553ff28f23a72b6208befcd",
                "api_password": "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",
                "rate_limit": 2,
            }
        }


# 加载配置
CONFIG = load_config()

# Shopify店铺详情
SHOP_NAME = CONFIG["shopify"]["shop_name"]  # 不带myshopify.com的版本
API_KEY = CONFIG["shopify"]["api_key"]
API_PASSWORD = CONFIG["shopify"]["api_password"]
# 获取自定义域名（如果有）
CUSTOM_DOMAIN = CONFIG["shopify"].get("custom_domain", "")


# Shopify API速率限制：每秒最多2次请求
class RateLimiter:
    """
    线程安全的速率限制器，用于控制API请求速率
    """

    def __init__(self, max_calls_per_second=2):
        self.max_calls_per_second = max_calls_per_second
        self.call_timestamps = []
        self.lock = threading.Lock()  # 添加线程锁
        self.print_lock = threading.Lock()  # 用于打印的锁

    def wait_if_needed(self):
        """
        检查是否需要等待以遵守速率限制（线程安全版本）
        """
        with self.lock:  # 使用锁确保线程安全
            now = time.time()

            # 清理超过1秒的旧时间戳
            self.call_timestamps = [ts for ts in self.call_timestamps if now - ts < 1]

            # 如果当前请求数已达到限制，则等待
            if len(self.call_timestamps) >= self.max_calls_per_second:
                # 计算需要等待的时间
                oldest_timestamp = min(self.call_timestamps)
                wait_time = 1 - (now - oldest_timestamp)

                if wait_time > 0:
                    with self.print_lock:  # 使用打印锁避免输出混乱
                        logging.info(f"达到API速率限制，等待 {wait_time:.2f} 秒...")
                        print(f"达到API速率限制，等待 {wait_time:.2f} 秒...")
                    time.sleep(wait_time)

            # 添加当前请求的时间戳
            self.call_timestamps.append(time.time())
            return True  # 返回成功标志


# 创建速率限制器实例
shopify_rate_limiter = RateLimiter(
    max_calls_per_second=CONFIG["shopify"].get("rate_limit", 2)
)

# 创建线程安全的打印和计数器
print_lock = threading.Lock()
counter_lock = threading.Lock()


def safe_print(message):
    """线程安全的打印函数"""
    with print_lock:
        print(message)


# 结果收集器类
class ResultCollector:
    """线程安全的结果收集器"""

    def __init__(self):
        self.lock = threading.Lock()
        self.updated = 0
        self.skipped = 0
        self.failed = 0
        self.results = []

    def add_result(self, result_type, product_info=None):
        """添加一个结果"""
        with self.lock:
            if result_type == "updated":
                self.updated += 1
            elif result_type == "skipped":
                self.skipped += 1
            elif result_type == "failed":
                self.failed += 1

            if product_info:
                self.results.append((result_type, product_info))

    def get_stats(self):
        """获取统计信息"""
        with self.lock:
            return {
                "updated": self.updated,
                "skipped": self.skipped,
                "failed": self.failed,
                "total": self.updated + self.skipped + self.failed,
            }


# 创建全局结果收集器
result_collector = ResultCollector()


def get_auth_headers():
    """生成Basic认证头"""
    auth_str = f"{API_KEY}:{API_PASSWORD}"
    auth_bytes = auth_str.encode("ascii")
    auth_b64_bytes = base64.b64encode(auth_bytes)
    auth_b64_str = auth_b64_bytes.decode("ascii")
    return {
        "Content-Type": "application/json",
        "Authorization": f"Basic {auth_b64_str}",
    }


def get_all_products(status="any"):
    """
    获取产品列表，遵守Shopify API速率限制（线程安全版本）

    参数:
        status (str): 产品状态过滤，可选值: "any"(所有), "active"(活跃), "archived"(已归档), "draft"(草稿)

    返回:
        list: 产品列表
    """
    all_products = []
    headers = get_auth_headers()

    # 添加状态过滤参数
    status_filter = f"&status={status}" if status != "any" else ""
    query_url = f"https://{SHOP_NAME}.myshopify.com/admin/api/2023-04/products.json?limit=250{status_filter}"

    with print_lock:
        print(f"开始获取产品...")
    logging.info("开始获取Shopify产品...")

    while query_url:
        with print_lock:
            print(f"正在获取: {query_url}")
        logging.info(f"正在获取: {query_url}")

        # 使用速率限制器控制请求速率
        shopify_rate_limiter.wait_if_needed()

        try:
            # 添加 verify=False 参数禁用SSL验证
            response = requests.get(
                query_url, headers=headers, timeout=10, verify=False
            )

            # 检查是否达到API限制
            if response.status_code == 429:
                # 如果收到429状态码（Too Many Requests），等待并重试
                retry_after = int(response.headers.get("Retry-After", 5))
                with print_lock:
                    print(f"达到API限制，等待 {retry_after} 秒后重试...")
                logging.warning(f"达到API限制，等待 {retry_after} 秒后重试...")
                time.sleep(retry_after)
                continue

            response.raise_for_status()  # 抛出其他HTTP错误

            data = response.json()
            products = data.get("products", [])
            all_products.extend(products)
            with print_lock:
                print(f"已获取 {len(all_products)} 个产品")
            logging.info(f"已获取 {len(all_products)} 个产品")

            # 检查API调用限制信息
            api_call_limit = response.headers.get("X-Shopify-Shop-Api-Call-Limit")
            if api_call_limit:
                logging.info(f"API调用限制: {api_call_limit}")
                # 如果接近限制，增加等待时间
                current, limit = map(int, api_call_limit.split("/"))
                if current > limit * 0.8:  # 如果已使用超过80%的配额
                    wait_time = 1.0  # 额外等待1秒
                    logging.warning(f"API调用接近限制，额外等待 {wait_time} 秒...")
                    time.sleep(wait_time)

            # 检查是否有下一页
            link_header = response.headers.get("Link")
            if link_header:
                links = link_header.split(",")
                next_link = None
                for link in links:
                    if 'rel="next"' in link:
                        next_link = link.split(";")[0].strip("<> ")
                        break
                query_url = next_link
            else:
                query_url = None

        except requests.exceptions.RequestException as e:
            error_msg = f"请求失败: {e}"
            with print_lock:
                print(error_msg)
            logging.error(error_msg)

            # 如果是连接错误或超时，等待后重试
            if isinstance(
                e, (requests.exceptions.ConnectionError, requests.exceptions.Timeout)
            ):
                wait_time = 5
                with print_lock:
                    print(f"连接错误或超时，等待 {wait_time} 秒后重试...")
                logging.warning(f"连接错误或超时，等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
                continue
            else:
                break

    return all_products


def get_product_gid(product_id):
    return f"gid://shopify/Product/{product_id}"


def get_product_metafields(product_gid, max_retries=5, base_wait=5):
    """获取产品SEO相关metafields（title_tag, description_tag），超时自动重试（线程安全版本）"""
    graphql_url = f"https://{SHOP_NAME}.myshopify.com/admin/api/2023-10/graphql.json"
    headers = get_auth_headers()
    query = {
        "query": f"""
        query {{
          product(id: \"{product_gid}\") {{
            metafields(first: 10, namespace: \"global\") {{
              edges {{
                node {{
                  id
                  key
                  value
                  type
                }}
              }}
            }}
          }}
        }}
        """
    }
    for attempt in range(1, max_retries + 1):
        shopify_rate_limiter.wait_if_needed()
        try:
            resp = requests.post(
                graphql_url, headers=headers, json=query, verify=False, timeout=20
            )
            if resp.status_code == 200:
                data = resp.json()
                metafields = {}
                edges = (
                    data.get("data", {})
                    .get("product", {})
                    .get("metafields", {})
                    .get("edges", [])
                )
                for edge in edges:
                    node = edge["node"]
                    metafields[node["key"]] = node
                return metafields
            else:
                with print_lock:
                    print(
                        f"[WARNING] 获取产品{product_gid} metafields失败: {resp.text}"
                    )
        except requests.exceptions.Timeout:
            wait_time = base_wait * attempt
            with print_lock:
                print(
                    f"[WARNING] 获取产品{product_gid} metafields超时，第{attempt}次重试，等待{wait_time}秒..."
                )
            time.sleep(wait_time)
        except Exception as e:
            with print_lock:
                print(f"[WARNING] 获取产品{product_gid} metafields失败: {e}")
            break
    return {}


def update_product_graphql(product_gid, update_fields, metafields=None):
    """使用GraphQL更新产品（线程安全版本）"""
    graphql_url = f"https://{SHOP_NAME}.myshopify.com/admin/api/2023-10/graphql.json"
    headers = get_auth_headers()
    input_obj = {"id": product_gid}
    input_obj.update(update_fields)
    if metafields:
        input_obj["metafields"] = metafields
    mutation = {
        "query": """
        mutation productUpdate($input: ProductInput!) {
          productUpdate(input: $input) {
            product { id title }
            userErrors { field message }
          }
        }
        """,
        "variables": {"input": input_obj},
    }
    shopify_rate_limiter.wait_if_needed()
    try:
        resp = requests.post(
            graphql_url, headers=headers, json=mutation, verify=False, timeout=10
        )
        if resp.status_code == 200:
            data = resp.json()
            if data.get("data", {}).get("productUpdate", {}).get("userErrors"):
                with print_lock:
                    print(
                        f"[ERROR] 产品{product_gid}更新失败: {data['data']['productUpdate']['userErrors']}"
                    )
                return False
            return True
        else:
            with print_lock:
                print(f"[ERROR] 产品{product_gid} GraphQL更新失败: {resp.text}")
    except Exception as e:
        with print_lock:
            print(f"[ERROR] 产品{product_gid} GraphQL请求异常: {e}")
    return False


def update_product_rest(product_id, update_fields):
    """用REST API更新产品（如body_html）"""
    url = f"https://{SHOP_NAME}.myshopify.com/admin/api/2023-04/products/{product_id}.json"
    headers = get_auth_headers()
    data = {"product": {"id": product_id}}
    data["product"].update(update_fields)
    shopify_rate_limiter.wait_if_needed()
    try:
        resp = requests.put(url, headers=headers, json=data, verify=False, timeout=10)
        return resp.status_code == 200
    except Exception as e:
        with print_lock:
            print(f"[ERROR] REST更新产品{product_id}异常: {e}")
    return False


def update_product_variant_options(product_id, old_option_name, new_option_name):
    """
    更新产品变体属性名称

    参数:
        product_id (int): 产品ID
        old_option_name (str): 旧的属性名称（如 "Colour"）
        new_option_name (str): 新的属性名称（如 "color"），如果为空字符串则删除该属性

    返回:
        bool: 更新是否成功
    """
    # 首先获取产品详情
    url = f"https://{SHOP_NAME}.myshopify.com/admin/api/2023-04/products/{product_id}.json"
    headers = get_auth_headers()

    try:
        shopify_rate_limiter.wait_if_needed()
        resp = requests.get(url, headers=headers, verify=False, timeout=10)

        if resp.status_code != 200:
            with print_lock:
                print(f"[ERROR] 获取产品{product_id}详情失败: {resp.text}")
            return False

        product_data = resp.json().get("product", {})
        options = product_data.get("options", [])
        variants = product_data.get("variants", [])

        # 检查是否有需要替换的属性名称
        option_found = False
        option_index = -1

        for i, option in enumerate(options):
            if option.get("name") == old_option_name:
                option_found = True
                option_index = i
                break

        if not option_found:
            with print_lock:
                print(f"[INFO] 产品{product_id}未找到属性 '{old_option_name}'")
            return False  # 没有找到需要替换的属性

        # 如果新属性名称为空，则删除该属性
        if new_option_name == "":
            with print_lock:
                print(f"[INFO] 产品{product_id}准备删除属性 '{old_option_name}'")

            # 检查是否只有一个属性，如果是则不能删除（Shopify要求至少有一个属性）
            if len(options) <= 1:
                with print_lock:
                    print(f"[WARNING] 产品{product_id}只有一个属性，无法删除。Shopify要求产品至少有一个变体属性。")
                return False

            # 删除属性 - 使用更安全的方法
            # 首先创建新的选项列表，排除要删除的选项
            new_options = []
            for i, option in enumerate(options):
                if i != option_index:
                    new_options.append(option)

            # 重新构建变体，只保留剩余的选项值
            new_variants = []
            for variant in variants:
                new_variant = {
                    "id": variant.get("id"),
                    "price": variant.get("price"),
                    "sku": variant.get("sku", ""),
                    "inventory_quantity": variant.get("inventory_quantity", 0),
                    "inventory_management": variant.get("inventory_management"),
                    "inventory_policy": variant.get("inventory_policy", "deny"),
                    "fulfillment_service": variant.get("fulfillment_service", "manual"),
                    "requires_shipping": variant.get("requires_shipping", True),
                    "taxable": variant.get("taxable", True),
                    "weight": variant.get("weight"),
                    "weight_unit": variant.get("weight_unit", "lb"),
                }

                # 重新分配选项值，跳过被删除的选项
                option_values = []
                for i in range(3):
                    option_key = f"option{i+1}"
                    if option_key in variant and i != option_index:
                        option_values.append(variant[option_key])

                # 重新分配到新的选项位置
                for i, value in enumerate(option_values):
                    new_variant[f"option{i+1}"] = value

                new_variants.append(new_variant)

            # 更新产品数据
            update_data = {
                "product": {
                    "id": product_id,
                    "options": new_options,
                    "variants": new_variants
                }
            }
        else:
            # 替换属性名称
            options[option_index]["name"] = new_option_name
            with print_lock:
                print(f"[INFO] 产品{product_id}找到属性 '{old_option_name}'，将替换为 '{new_option_name}'")

            # 更新产品 - 只更新选项名称，不修改变体
            update_data = {
                "product": {
                    "id": product_id,
                    "options": options
                }
            }

        shopify_rate_limiter.wait_if_needed()
        update_resp = requests.put(url, headers=headers, json=update_data, verify=False, timeout=10)

        if update_resp.status_code == 200:
            with print_lock:
                if new_option_name == "":
                    print(f"[SUCCESS] 产品{product_id}属性 '{old_option_name}' 删除成功")
                else:
                    print(f"[SUCCESS] 产品{product_id}属性名称更新成功: '{old_option_name}' → '{new_option_name}'")
            return True
        else:
            with print_lock:
                print(f"[ERROR] 产品{product_id}属性更新失败: {update_resp.text}")
                # 打印详细的错误信息以便调试
                try:
                    error_data = update_resp.json()
                    if "errors" in error_data:
                        print(f"[ERROR] 详细错误信息: {error_data['errors']}")
                except:
                    pass
            return False

    except Exception as e:
        with print_lock:
            print(f"[ERROR] 更新产品{product_id}属性名称时发生异常: {e}")
            import traceback
            print(f"[DEBUG] 异常详情: {traceback.format_exc()}")
        return False


def extract_weight_from_description(description):
    """
    从产品描述中提取重量信息，支持HTML格式

    参数:
        description (str): 产品描述HTML

    返回:
        tuple: (重量值（以磅为单位）, 原始单位), 如果未找到则返回(None, None)
    """
    import re

    if not description:
        return None, None

    # 匹配HTML中的重量信息，包括在列表项中的情况
    # 支持以下格式:
    # 1. 纯文本: "Weight: 43 lbs" 或 "Weight: 20 kg"
    # 2. HTML标签中: "<strong>Weight:</strong> 43 lbs"
    # 3. 列表项中: "<li><strong>Weight:</strong> 43 lbs.</li>"

    # 先尝试匹配磅单位
    lb_pattern = r"(?:<[^>]*>)*\s*Weight\s*:?\s*(?:<[^>]*>)*\s*(\d+(?:\.\d+)?)\s*(?:lbs?\.?|pounds?)(?:<[^>]*>)*"  # 支持lb, lbs, pound, pounds格式，以及带点的lbs.
    lb_match = re.search(lb_pattern, description, re.IGNORECASE)

    if lb_match:
        try:
            weight_value = float(lb_match.group(1))  # 提取数字部分并转换为浮点数
            return weight_value, "lb"
        except (ValueError, TypeError):
            pass

    # 如果没有找到磅单位，尝试匹配千克单位
    kg_pattern = r"(?:<[^>]*>)*\s*Weight\s*:?\s*(?:<[^>]*>)*\s*(\d+(?:\.\d+)?)\s*(?:kg\.?|kilograms?|公斤)(?:<[^>]*>)*"  # 支持kg, kilograms, 公斤格式
    kg_match = re.search(kg_pattern, description, re.IGNORECASE)

    if kg_match:
        try:
            weight_value = float(kg_match.group(1))  # 提取数字部分并转换为浮点数
            # 将千克转换为磅 (1 kg = 2.20462 lb)
            weight_in_lb = weight_value * 2.20462
            return weight_in_lb, "kg"
        except (ValueError, TypeError):
            pass

    # 尝试匹配一般的重量格式，不指定单位
    general_pattern = r"(?:<[^>]*>)*\s*Weight\s*:?\s*(?:<[^>]*>)*\s*(\d+(?:\.\d+)?)\s*([a-zA-Z]+)(?:<[^>]*>)*"
    general_match = re.search(general_pattern, description, re.IGNORECASE)

    if general_match:
        try:
            weight_value = float(general_match.group(1))
            unit = general_match.group(2).lower()

            # 处理不同的单位
            if unit in ["lb", "lbs", "pound", "pounds"]:
                return weight_value, "lb"
            elif unit in ["kg", "kgs", "kilogram", "kilograms", "公斤"]:
                return weight_value * 2.20462, "kg"
            elif unit in ["g", "gram", "grams", "克"]:
                return weight_value * 0.00220462, "g"
            elif unit in ["oz", "ounce", "ounces", "盎司"]:
                return weight_value * 0.0625, "oz"  # 1 oz = 0.0625 lb
        except (ValueError, TypeError):
            pass

    return None, None


def update_product_weight(product_id, product_gid, weight_value):
    """
    更新产品重量

    参数:
        product_id (int): 产品ID
        product_gid (str): 产品全局ID
        weight_value (float): 重量值（磅）

    返回:
        bool: 更新是否成功
    """
    # 确保重量值是有效的正数
    if weight_value is None or weight_value <= 0:
        with print_lock:
            print(f"[ERROR] 产品{product_id}重量值无效: {weight_value}")
        return False
    # 使用GraphQL API更新产品变体的重量
    graphql_url = f"https://{SHOP_NAME}.myshopify.com/admin/api/2023-10/graphql.json"
    headers = get_auth_headers()

    # 首先获取产品的变体ID
    query = {
        "query": f"""
        query {{
          product(id: \"{product_gid}\") {{
            variants(first: 10) {{
              edges {{
                node {{
                  id
                  inventoryItem {{
                    id
                  }}
                }}
              }}
            }}
          }}
        }}
        """
    }

    shopify_rate_limiter.wait_if_needed()
    try:
        resp = requests.post(
            graphql_url, headers=headers, json=query, verify=False, timeout=20
        )
        if resp.status_code == 200:
            data = resp.json()
            variants = (
                data.get("data", {})
                .get("product", {})
                .get("variants", {})
                .get("edges", [])
            )

            if not variants:
                with print_lock:
                    print(f"[WARNING] 产品{product_id}没有变体，无法更新重量")
                return False

            success = True
            # 更新每个变体的重量
            for variant_edge in variants:
                variant = variant_edge.get("node", {})
                inventory_item_id = variant.get("inventoryItem", {}).get("id")

                if not inventory_item_id:
                    continue

                # 更新库存项的重量 - 使用正确的嵌套结构
                mutation = {
                    "query": """
                    mutation inventoryItemUpdate($id: ID!, $input: InventoryItemInput!) {
                      inventoryItemUpdate(id: $id, input: $input) {
                        inventoryItem {
                          id
                          weight
                          weightUnit
                        }
                        userErrors {
                          field
                          message
                        }
                      }
                    }
                    """,
                    "variables": {
                        "id": inventory_item_id,
                        "input": {
                            "tracked": True,
                            "weight": weight_value,
                            "weightUnit": "POUNDS",
                            "harmonizedSystemCode": None,
                            "countryCodeOfOrigin": None,
                            "provinceCodeOfOrigin": None,
                            "requiresShipping": True,
                        },
                    },
                }

                # 发送更新请求

                shopify_rate_limiter.wait_if_needed()
                update_resp = requests.post(
                    graphql_url,
                    headers=headers,
                    json=mutation,
                    verify=False,
                    timeout=20,
                )

                if update_resp.status_code != 200:
                    success = False
                    with print_lock:
                        print(
                            f"[ERROR] 更新产品{product_id}变体重量失败，HTTP状态码: {update_resp.status_code}"
                        )
                else:
                    response_data = update_resp.json()
                    user_errors = (
                        response_data.get("data", {})
                        .get("inventoryItemUpdate", {})
                        .get("userErrors", [])
                    )

                    if user_errors:
                        success = False
                        with print_lock:
                            print(
                                f"[ERROR] 更新产品{product_id}变体重量失败，API错误: {user_errors}"
                            )
                    else:
                        inventory_item = (
                            response_data.get("data", {})
                            .get("inventoryItemUpdate", {})
                            .get("inventoryItem", {})
                        )
                        updated_weight = inventory_item.get("weight")
                        updated_unit = inventory_item.get("weightUnit")

                        if updated_weight is not None and float(updated_weight) > 0:
                            with print_lock:
                                print(
                                    f"[SUCCESS] 更新产品{product_id}变体重量成功: 重量={updated_weight}，单位={updated_unit}"
                                )
                        else:
                            # 如果返回的重量为空或0，尝试使用REST API直接更新变体重量
                            with print_lock:
                                print(
                                    f"[WARNING] GraphQL API未能更新重量，尝试使用REST API更新..."
                                )

                            # 获取变体ID
                            variant_id = variant.get("id", "").split("/")[-1]
                            if variant_id:
                                # 使用REST API更新变体
                                rest_url = f"https://{SHOP_NAME}.myshopify.com/admin/api/2023-04/variants/{variant_id}.json"
                                rest_data = {
                                    "variant": {
                                        "id": variant_id,
                                        "weight": weight_value,
                                        "weight_unit": "lb",
                                    }
                                }
                                shopify_rate_limiter.wait_if_needed()
                                try:
                                    rest_resp = requests.put(
                                        rest_url,
                                        headers=headers,
                                        json=rest_data,
                                        verify=False,
                                        timeout=10,
                                    )
                                    if rest_resp.status_code == 200:
                                        rest_data = rest_resp.json()
                                        updated_variant = rest_data.get("variant", {})
                                        with print_lock:
                                            print(
                                                f"[SUCCESS] 使用REST API更新产品{product_id}变体重量成功: 重量={updated_variant.get('weight')}，单位={updated_variant.get('weight_unit')}"
                                            )
                                    else:
                                        with print_lock:
                                            print(
                                                f"[ERROR] REST API更新产品{product_id}变体重量失败: {rest_resp.text}"
                                            )
                                except Exception as e:
                                    with print_lock:
                                        print(
                                            f"[ERROR] REST API更新产品{product_id}变体重量异常: {e}"
                                        )
                            else:
                                with print_lock:
                                    print(
                                        f"[ERROR] 无法获取产品{product_id}变体ID，无法使用REST API更新"
                                    )

            return success
        else:
            with print_lock:
                print(f"[ERROR] 获取产品{product_id}变体失败: {resp.text}")
    except Exception as e:
        with print_lock:
            print(f"[ERROR] 更新产品{product_id}重量时发生异常: {e}")

    return False


def process_single_product(
    product,
    old_kw=None,
    new_kw=None,
    group=None,
    product_index=None,
    total_products=None,
    update_weight=False,
    replace_mode="html",
    update_variant_options=False,
):
    """
    处理单个产品（线程安全版本）
    支持关键词替换或重量更新

    参数:
        product (dict): 产品信息
        old_kw (str, optional): 要替换的旧关键词
        new_kw (str, optional): 新的关键词
        group (str, optional): 要替换的字段组 ('title_group' 或 'desc_group')
        product_index (int, optional): 产品索引，用于显示进度
        total_products (int, optional): 总产品数，用于显示进度
        update_weight (bool, optional): 是否更新重量

    返回:
        tuple: (result_type, product_info) 其中result_type是'updated', 'skipped'或'failed'
    """
    try:
        product_id = product["id"]  # 数字ID
        product_gid = get_product_gid(product_id)  # 全局ID
        update_fields = {}
        metafields = []
        need_update = False
        rest_success = True
        graphql_success = True
        weight_updated = False

        # 如果产品信息不完整，需要获取详情
        if (
            ("title" not in product and group == "title_group")
            or ("body_html" not in product and (group == "desc_group" or update_weight))
            or update_weight
        ):
            # 用REST API获取单个产品详情
            url = f"https://{SHOP_NAME}.myshopify.com/admin/api/2023-04/products/{product_id}.json"
            headers = get_auth_headers()
            try:
                shopify_rate_limiter.wait_if_needed()
                resp = requests.get(url, headers=headers, verify=False, timeout=10)
                if resp.status_code == 200:
                    product = resp.json().get("product", product)
                else:
                    with print_lock:
                        print(f"获取产品{product_id}详情失败: {resp.text}")
                    return "failed", {
                        "id": product_id,
                        "title": product.get("title", f"ID: {product_id}"),
                    }
            except Exception as e:
                with print_lock:
                    print(f"获取产品{product_id}详情异常: {e}")
                return "failed", {
                    "id": product_id,
                    "title": product.get("title", f"ID: {product_id}"),
                }

        # 处理变体属性名称替换
        if update_variant_options and old_kw and new_kw is not None:
            variant_success = update_product_variant_options(product_id, old_kw, new_kw)
            if variant_success:
                need_update = True
                with print_lock:
                    if new_kw == "":
                        print(f"✓ 产品 {product.get('title', '')}（ID: {product_id}）属性 '{old_kw}' 已删除")
                    else:
                        print(f"✓ 产品 {product.get('title', '')}（ID: {product_id}）属性已更新: '{old_kw}' → '{new_kw}'")

        # 处理重量更新
        if update_weight:
            body_html = product.get("body_html", "")
            weight_value, original_unit = extract_weight_from_description(body_html)

            if weight_value:
                # 更新产品重量
                weight_success = update_product_weight(
                    product_id, product_gid, weight_value
                )
                if weight_success:
                    weight_updated = True
                    need_update = True

                    # 根据原始单位显示不同的消息
                    if original_unit == "kg":
                        with print_lock:
                            print(
                                f"✓ 产品 {product.get('title', '')}（ID: {product_id}）重量已从 {weight_value/2.20462:.2f} 千克转换并更新为 {weight_value:.2f} 磅"
                            )
                    elif original_unit == "g":
                        with print_lock:
                            print(
                                f"✓ 产品 {product.get('title', '')}（ID: {product_id}）重量已从 {weight_value/0.00220462:.2f} 克转换并更新为 {weight_value:.2f} 磅"
                            )
                    elif original_unit == "oz":
                        with print_lock:
                            print(
                                f"✓ 产品 {product.get('title', '')}（ID: {product_id}）重量已从 {weight_value/0.0625:.2f} 盎司转换并更新为 {weight_value:.2f} 磅"
                            )
                    else:
                        with print_lock:
                            print(
                                f"✓ 产品 {product.get('title', '')}（ID: {product_id}）重量已更新为 {weight_value:.2f} 磅"
                            )
                else:
                    with print_lock:
                        print(
                            f"× 产品 {product.get('title', '')}（ID: {product_id}）重量更新失败"
                        )
            else:
                with print_lock:
                    print(
                        f"- 产品 {product.get('title', '')}（ID: {product_id}）描述中未找到重量信息"
                    )

        # 处理标题组
        if group == "title_group":
            val = product.get("title", "")
            if old_kw in val:
                if new_kw == "":
                    # 删除关键词模式：直接删除旧关键词
                    update_fields["title"] = val.replace(old_kw, "").strip()
                    # 清理多余的空格
                    update_fields["title"] = " ".join(update_fields["title"].split())
                else:
                    # 替换关键词模式
                    update_fields["title"] = val.replace(old_kw, new_kw)
                need_update = True
            metafields_data = get_product_metafields(product_gid)
            node = metafields_data.get("title_tag")
            if node and old_kw in node["value"]:
                if new_kw == "":
                    # 删除关键词模式
                    new_value = node["value"].replace(old_kw, "").strip()
                    new_value = " ".join(new_value.split())  # 清理多余的空格
                else:
                    # 替换关键词模式
                    new_value = node["value"].replace(old_kw, new_kw)
                metafields.append({"id": node["id"], "value": new_value})
                need_update = True
            elif product.get("title") and old_kw in product["title"]:
                if "seo" not in update_fields:
                    update_fields["seo"] = {}
                if new_kw == "":
                    # 删除关键词模式
                    seo_title = product["title"].replace(old_kw, "").strip()
                    update_fields["seo"]["title"] = " ".join(seo_title.split())
                else:
                    # 替换关键词模式
                    update_fields["seo"]["title"] = product["title"].replace(
                        old_kw, new_kw
                    )
                need_update = True
            if (
                need_update and not weight_updated
            ):  # 如果只是更新了重量，不需要再更新标题
                graphql_success = update_product_graphql(
                    product_gid, update_fields, metafields if metafields else None
                )

        # 处理描述组
        elif group == "desc_group":
            val = product.get("body_html", "")
            found = False

            if replace_mode == "text":
                # 纯文本模式：在HTML中查找和替换纯文本内容，保留HTML结构
                import html
                from html import unescape

                # 移除HTML标签，获取纯文本用于检查
                clean_text = re.sub(r"<[^>]+>", "", val)
                # 解码HTML实体
                clean_text = unescape(clean_text)
                # 标准化空白字符
                clean_text = re.sub(r"\s+", " ", clean_text).strip()

                # 检查纯文本中是否包含要替换的内容
                if old_kw in clean_text:
                    found = True

                    # 方法1：直接在HTML中替换（适用于连续文本）
                    html_content = val
                    if old_kw in html_content:
                        if new_kw == "":
                            update_fields["body_html"] = html_content.replace(
                                old_kw, ""
                            )
                        else:
                            update_fields["body_html"] = html_content.replace(
                                old_kw, new_kw
                            )
                        need_update = True
                    else:
                        # 方法2：处理HTML实体编码的情况
                        decoded_html = unescape(html_content)
                        if old_kw in decoded_html:
                            if new_kw == "":
                                replaced = decoded_html.replace(old_kw, "")
                            else:
                                replaced = decoded_html.replace(old_kw, new_kw)
                            # 重新编码必要的HTML字符，但保持结构
                            update_fields["body_html"] = replaced
                            need_update = True
                        else:
                            # 方法3：处理被HTML标签分割的文本
                            def create_flexible_pattern(text):
                                # 转义正则表达式特殊字符
                                escaped_chars = []
                                for char in text:
                                    if char in r"\.^$*+?{}[]|()":
                                        escaped_chars.append("\\" + char)
                                    else:
                                        escaped_chars.append(char)

                                # 在字符之间允许HTML标签和空白字符
                                pattern_parts = []
                                for i, char in enumerate(escaped_chars):
                                    if i > 0:
                                        # 在字符之间允许HTML标签、空白字符和HTML实体
                                        pattern_parts.append(r"(?:<[^>]*>|\s|&[^;]+;)*")
                                    pattern_parts.append(char)

                                return "".join(pattern_parts)

                            try:
                                pattern = create_flexible_pattern(old_kw)
                                if new_kw == "":
                                    # 删除模式
                                    update_fields["body_html"] = re.sub(
                                        pattern,
                                        "",
                                        html_content,
                                        flags=re.IGNORECASE | re.DOTALL,
                                    )
                                else:
                                    # 替换模式
                                    update_fields["body_html"] = re.sub(
                                        pattern,
                                        new_kw,
                                        html_content,
                                        flags=re.IGNORECASE | re.DOTALL,
                                    )
                                need_update = True
                            except Exception as e:
                                with print_lock:
                                    print(
                                        f"[WARNING] 产品{product_id}正则表达式替换失败: {e}"
                                    )
                                    print(f"[INFO] 请尝试使用HTML模式或检查关键词格式")

                    if need_update:
                        # 清理可能产生的多余空格
                        update_fields["body_html"] = re.sub(
                            r"\s+", " ", update_fields["body_html"]
                        ).strip()
                        update_fields["body_html"] = re.sub(
                            r">\s+<", "><", update_fields["body_html"]
                        )
            elif replace_mode == "smart":
                # 智能文本模式：使用HTML AST精准替换
                import html
                from html import unescape

                # 移除HTML标签，获取纯文本用于检查
                clean_text = re.sub(r"<[^>]+>", "", val)
                # 解码HTML实体
                clean_text = unescape(clean_text)
                # 标准化空白字符
                clean_text = re.sub(r"\s+", " ", clean_text).strip()

                # 检查纯文本中是否包含要替换的内容
                if old_kw in clean_text:
                    found = True

                    # 使用智能HTML文本替换器
                    try:
                        # 启用调试模式来查看详细过程
                        debug_mode = False  # 可以设置为True来启用调试

                        with print_lock:
                            print(f"[DEBUG] 产品{product_id}开始智能替换")
                            print(f"[DEBUG] 原始HTML长度: {len(val)}")
                            print(f"[DEBUG] 纯文本内容: {clean_text[:200]}...")
                            print(f"[DEBUG] 查找文本: '{old_kw}'")

                        new_html, replaced_count = smart_text_replace(
                            val, old_kw, new_kw if new_kw else "", debug=debug_mode
                        )

                        if replaced_count > 0:
                            update_fields["body_html"] = new_html
                            need_update = True

                            with print_lock:
                                print(
                                    f"[INFO] 产品{product_id}智能替换成功，替换了{replaced_count}处"
                                )
                                print(f"[DEBUG] 新HTML长度: {len(new_html)}")
                        else:
                            with print_lock:
                                print(
                                    f"[WARNING] 产品{product_id}智能替换未找到匹配内容"
                                )
                                print(f"[DEBUG] 可能原因：文本格式不匹配或HTML结构复杂")
                                # 显示实际的HTML片段用于调试
                                print(f"[DEBUG] HTML片段: {val[:300]}...")

                    except Exception as e:
                        with print_lock:
                            print(f"[WARNING] 产品{product_id}智能替换失败: {e}")
                            print(f"[INFO] 请尝试使用其他替换模式")
                            import traceback

                            print(f"[DEBUG] 错误详情: {traceback.format_exc()}")

            else:
                # HTML代码模式：精确匹配HTML代码
                if old_kw in val:
                    found = True
                    if new_kw == "":
                        # 删除关键词模式：直接删除旧关键词
                        update_fields["body_html"] = val.replace(old_kw, "")
                        # 清理HTML中的多余空格，但保留HTML结构
                        update_fields["body_html"] = re.sub(
                            r"\s+", " ", update_fields["body_html"]
                        ).strip()
                    else:
                        # 替换关键词模式
                        update_fields["body_html"] = val.replace(old_kw, new_kw)
                    need_update = True
                else:
                    # 如果直接匹配失败，尝试HTML实体转换
                    import html

                    # 将输入的HTML实体转换为Unicode字符
                    old_kw_decoded = html.unescape(old_kw)
                    if old_kw_decoded in val:
                        found = True
                        if new_kw == "":
                            # 删除关键词模式
                            update_fields["body_html"] = val.replace(old_kw_decoded, "")
                            update_fields["body_html"] = re.sub(
                                r"\s+", " ", update_fields["body_html"]
                            ).strip()
                        else:
                            # 替换关键词模式
                            new_kw_decoded = html.unescape(new_kw) if new_kw else ""
                            update_fields["body_html"] = val.replace(
                                old_kw_decoded, new_kw_decoded
                            )
                        need_update = True

            # 如果没有找到匹配，跳过处理
            metafields_data = get_product_metafields(product_gid)
            node = metafields_data.get("description_tag")
            if node and old_kw in node["value"]:
                if new_kw == "":
                    # 删除关键词模式
                    new_value = node["value"].replace(old_kw, "")
                    new_value = re.sub(r"\s+", " ", new_value).strip()  # 清理多余的空格
                else:
                    # 替换关键词模式
                    new_value = node["value"].replace(old_kw, new_kw)
                metafields.append({"id": node["id"], "value": new_value})
                need_update = True
            elif product.get("body_html") and old_kw in product["body_html"]:
                if "seo" not in update_fields:
                    update_fields["seo"] = {}
                if new_kw == "":
                    # 删除关键词模式
                    seo_desc = product["body_html"].replace(old_kw, "")
                    update_fields["seo"]["description"] = re.sub(
                        r"\s+", " ", seo_desc
                    ).strip()
                else:
                    # 替换关键词模式
                    update_fields["seo"]["description"] = product["body_html"].replace(
                        old_kw, new_kw
                    )
                need_update = True
            if "body_html" in update_fields:
                rest_success = update_product_rest(
                    product_id, {"body_html": update_fields["body_html"]}
                )
            if (
                metafields and not weight_updated
            ):  # 如果只是更新了重量，不需要再更新描述
                graphql_success = update_product_graphql(product_gid, {}, metafields)

        # 处理结果
        if need_update:
            if (rest_success and graphql_success) or weight_updated:
                # 生成产品链接
                product_handle = product.get("handle", "")
                product_link = (
                    f"https://{CUSTOM_DOMAIN}/products/{product_handle}"
                    if CUSTOM_DOMAIN
                    else f"https://{SHOP_NAME}.myshopify.com/products/{product_handle}"
                )

                # 显示进度信息
                progress_info = ""
                if product_index is not None and total_products is not None:
                    progress_info = f" - 进度: {product_index+1}/{total_products}"

                with print_lock:
                    if not weight_updated:  # 如果是关键词更新，显示更新成功信息
                        print(
                            f"✓ 产品 {product.get('title', '')}（ID: {product_id}）已更新{progress_info}"
                        )
                    print(f"  产品链接: {product_link}")

                return "updated", {
                    "id": product_id,
                    "title": product.get("title", ""),
                    "link": product_link,
                }
            else:
                with print_lock:
                    print(
                        f"× 产品 {product.get('title', '')}（ID: {product_id}）更新失败"
                    )
                return "failed", {"id": product_id, "title": product.get("title", "")}
        else:
            return "skipped", {"id": product_id, "title": product.get("title", "")}

    except Exception as e:
        with print_lock:
            print(f"处理产品时发生错误: {str(e)}")
            print(traceback.format_exc())
        return "failed", {
            "id": product.get("id", "unknown"),
            "title": product.get("title", "unknown"),
        }


def setup_logging():
    # 创建日志目录
    log_dir = "logs"
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    log_file = os.path.join(
        log_dir, f"shopify_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    )
    log_format = "%(asctime)s [%(levelname)s] %(message)s"
    date_format = "%Y-%m-%d %H:%M:%S"
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        datefmt=date_format,
        handlers=[
            logging.FileHandler(log_file, encoding="utf-8"),
            logging.StreamHandler(),  # 同时输出到控制台
        ],
    )
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    logging.info(f"日志将同时输出到控制台和文件: {log_file}")
    return log_file


def main():
    # 初始化日志
    setup_logging()

    try:
        # 显示当前使用的店铺信息
        print(f"当前使用的店铺: {SHOP_NAME}")
        if CUSTOM_DOMAIN:
            print(f"自定义域名: {CUSTOM_DOMAIN}")
        print(f"API速率限制: 每秒{CONFIG['shopify'].get('rate_limit', 2)}次请求")
        print("-" * 50)

        print("请选择操作模式：")
        print("1. 批量修改所有产品")
        print("2. 只修改单个产品")
        mode = input("请输入数字选择操作模式：").strip()
        if mode not in ["1", "2"]:
            print("无效选择，退出。")
            return

        # 如果选择批量模式，询问产品状态
        product_status = "any"
        if mode == "1":
            print("\n请选择要处理的产品状态：")
            print("1. 所有产品")
            print("2. 只处理活跃的产品")
            print("3. 只处理已归档的产品")
            print("4. 只处理草稿产品")
            status_choice = input("请输入数字选择产品状态（默认为所有产品）：").strip()

            status_map = {"1": "any", "2": "active", "3": "archived", "4": "draft"}

            if status_choice in status_map:
                product_status = status_map[status_choice]
                status_display = {
                    "any": "所有",
                    "active": "活跃的",
                    "archived": "已归档的",
                    "draft": "草稿",
                }
                print(f"将处理{status_display[product_status]}产品...")

        print("请选择要执行的操作：")
        print("1. 替换关键词")
        print("2. 从描述中提取并更新产品重量")
        print("3. 同步产品标题和描述到SEO标题和SEO描述")
        print("4. 同步产品标题到图片alt值")
        print("5. AI优化产品信息（标题、描述、Handle）符合GMC政策规范")
        print("6. 替换产品变体属性名称")
        operation = input("请输入数字选择操作：").strip()
        if operation not in ["1", "2", "3", "4", "5", "6"]:
            print("无效选择，退出。")
            return

        # 关键词替换操作
        if operation == "1":
            print("请选择要替换的字段：")
            print("1. 标题 (title + SEO title)")
            print("2. 描述 (body_html + SEO description)")
            field_map = {"1": "title_group", "2": "desc_group"}
            field_choice = input("请输入数字选择字段：").strip()
            if field_choice not in field_map:
                print("无效选择，退出。")
                return

            group = field_map[field_choice]

            # 如果选择描述，询问替换模式
            replace_mode = "html"  # 默认HTML模式
            if group == "desc_group":
                print("\n请选择替换模式：")
                print("1. HTML代码模式 (精确匹配HTML标签和代码)")
                print("2. 纯文本模式 (只匹配显示的文本内容，忽略HTML标签)")
                print("3. 智能文本模式 (跨标签匹配文本，完全保留HTML结构)")
                mode_choice = input(
                    "请输入数字选择模式（默认为HTML代码模式）："
                ).strip()
                if mode_choice == "2":
                    replace_mode = "text"
                    print("已选择纯文本模式 - 将忽略HTML标签，只匹配显示的文本内容")
                elif mode_choice == "3":
                    replace_mode = "smart"
                    print("已选择智能文本模式 - 将跨标签匹配文本，完全保留HTML结构")
                else:
                    print("已选择HTML代码模式 - 将精确匹配HTML代码")
            old_kw = input("请输入要替换的旧关键词（区分大小写）：").strip()
            new_kw = input("请输入新的关键词（留空则删除旧关键词）：").strip()

            if new_kw == "":
                # 删除关键词模式
                if group == "title_group":
                    print(
                        f"将批量删除所有商品的 标题(title) 和 SEO标题(SEO title) 字段中的 '{old_kw}' ..."
                    )
                else:
                    print(
                        f"将批量删除所有商品的 描述(body_html) 和 SEO描述(SEO description) 字段中的 '{old_kw}' ..."
                    )
            else:
                # 替换关键词模式
                if group == "title_group":
                    print(
                        f"将批量替换所有商品的 标题(title) 和 SEO标题(SEO title) 字段中的 '{old_kw}' 为 '{new_kw}' ..."
                    )
                else:
                    print(
                        f"将批量替换所有商品的 描述(body_html) 和 SEO描述(SEO description) 字段中的 '{old_kw}' 为 '{new_kw}' ..."
                    )
        # 更新重量操作
        elif operation == "2":
            print(
                "将从产品描述中提取重量信息（格式如 'Weight: 198 lbs'）并更新到产品属性中..."
            )
            group = None
            old_kw = None
            new_kw = None
            replace_mode = "html"
        # SEO同步操作
        elif operation == "3":
            print("将同步产品标题和描述到SEO标题和SEO描述...")
            group = None
            old_kw = None
            new_kw = None
            replace_mode = "html"
        # 图片alt同步操作
        elif operation == "4":
            print("将同步产品标题到图片alt值...")
            group = None
            old_kw = None
            new_kw = None
            replace_mode = "html"
        # AI产品优化操作
        elif operation == "5":
            print("将使用AI优化产品信息，使其符合谷歌GMC政策规范...")
            print("优化内容包括：")
            print("- 产品标题：确保准确描述产品，符合GMC标题规范")
            print("- 产品描述：优化内容结构，保留图片引用，符合政策要求")
            print("- 产品Handle：基于优化后的标题生成SEO友好的URL")
            print("注意：此操作会调用AI模型，请确保网络连接正常")

            confirm = input("确认开始AI优化？(y/N): ").strip().lower()
            if confirm != 'y':
                print("操作已取消")
                return

            group = None
            old_kw = None
            new_kw = None
            replace_mode = "html"
        # 变体属性名称替换操作
        else:  # operation == "6"
            print("将批量替换产品变体属性名称...")
            print("例如：将 'Colour' 替换为 'color'，或将 'Size' 替换为 'size'")

            old_kw = input("请输入要替换的旧属性名称（区分大小写）：").strip()
            new_kw = input("请输入新的属性名称（留空则删除该属性）：").strip()

            if not old_kw:
                print("旧属性名称不能为空，退出。")
                return

            if new_kw == "":
                print(f"将批量删除所有产品的变体属性 '{old_kw}' ...")
            else:
                print(f"将批量替换所有产品的变体属性 '{old_kw}' 为 '{new_kw}' ...")

            group = None
            replace_mode = "html"

        # 获取产品列表
        if mode == "1":
            products = get_all_products(status=product_status)
        else:
            single_id = input("请输入要修改的产品ID：").strip()
            if not single_id.isdigit():
                print("产品ID必须为数字，退出。")
                return
            # 只查一个产品
            products = [{"id": int(single_id)}]

        if not products:
            error_msg = "未能获取到任何产品。请检查您的API密钥和密码是否正确。"
            print(error_msg)
            return

        total_products = len(products)
        print(f"总共找到 {total_products} 个产品")

        # 如果是批量模式，询问线程数
        max_workers = 1
        if mode == "1" and total_products > 1:
            if operation in ["3", "4", "5"]:  # SEO同步、图片alt同步或AI优化操作
                if operation == "3":
                    operation_name = "SEO同步"
                elif operation == "4":
                    operation_name = "图片alt同步"
                else:
                    operation_name = "AI产品优化"
                print(f"{operation_name}操作建议使用较少线程以避免API限制")
                try:
                    max_workers_input = input(
                        f"请输入并行处理的线程数（1-5，默认为2）："
                    ).strip()
                    if max_workers_input:
                        max_workers = int(max_workers_input)
                        max_workers = max(1, min(5, max_workers))  # 限制在1-5之间
                    else:
                        max_workers = 2  # 默认值为2
                except ValueError:
                    max_workers = 2  # 默认值为2
            else:
                try:
                    max_workers_input = input(
                        f"请输入并行处理的线程数（1-10，默认为3）："
                    ).strip()
                    if max_workers_input:
                        max_workers = int(max_workers_input)
                        max_workers = max(1, min(10, max_workers))  # 限制在1-10之间
                    else:
                        max_workers = 3  # 默认值
                except ValueError:
                    max_workers = 3  # 默认值
                print("输入无效，使用默认值3")

            print(f"将使用 {max_workers} 个线程并行处理 {total_products} 个产品")

        # 重置结果收集器
        global result_collector
        result_collector = ResultCollector()

        # 使用线程池并行处理产品
        print("\n开始处理产品...")
        start_time = time.time()

        if operation == "3":
            # SEO同步操作
            seo_sync.sync_seo_for_products(
                products,
                CONFIG,
                shopify_rate_limiter,
                print_lock,
                result_collector,
                max_workers,
            )
        elif operation == "4":
            # 图片alt同步操作
            media_alt_sync.sync_media_alt_for_products(
                products,
                CONFIG,
                shopify_rate_limiter,
                print_lock,
                result_collector,
                max_workers,
            )
        elif operation == "5":
            # AI产品优化操作
            product_optimizer.optimize_products_for_gmc(
                products,
                CONFIG,
                shopify_rate_limiter,
                print_lock,
                result_collector,
                max_workers,
            )
        else:
            # 关键词替换、重量更新或变体属性名称替换操作
            if max_workers > 1:
                # 多线程模式
                with concurrent.futures.ThreadPoolExecutor(
                    max_workers=max_workers
                ) as executor:
                    # 提交所有任务
                    future_to_index = {}
                    for i, product in enumerate(products):
                        future = executor.submit(
                            process_single_product,
                            product,
                            old_kw,
                            new_kw,
                            group,
                            i,
                            total_products,
                            operation == "2",  # 是否更新重量
                            replace_mode,  # 替换模式
                            operation == "6",  # 是否更新变体属性名称
                        )
                        future_to_index[future] = i

                    # 处理完成的任务
                    for future in concurrent.futures.as_completed(future_to_index):
                        product_index = future_to_index[future]
                        try:
                            result_type, product_info = future.result()
                            result_collector.add_result(result_type, product_info)

                            # 定期显示进度
                            stats = result_collector.get_stats()
                            if (stats["total"] % 10 == 0) or (
                                stats["total"] == total_products
                            ):
                                elapsed = time.time() - start_time
                                with print_lock:
                                    print(
                                        f"\n当前进度: {stats['total']}/{total_products} "
                                        f"(成功: {stats['updated']}, 跳过: {stats['skipped']}, "
                                        f"失败: {stats['failed']}) - 耗时: {elapsed:.1f}秒"
                                    )
                        except Exception as e:
                            with print_lock:
                                print(
                                    f"处理产品 {product_index+1}/{total_products} 时发生错误: {str(e)}"
                                )
                            result_collector.add_result("failed")
            else:
                # 单线程模式
                for i, product in enumerate(products):
                    result_type, product_info = process_single_product(
                        product,
                        old_kw,
                        new_kw,
                        group,
                        i,
                        total_products,
                        operation == "2",  # 是否更新重量
                        replace_mode,  # 替换模式
                        operation == "6",  # 是否更新变体属性名称
                    )
                    result_collector.add_result(result_type, product_info)

        # 显示最终结果
        elapsed_time = time.time() - start_time
        stats = result_collector.get_stats()
        print("\n" + "=" * 50)
        print(
            f"批量处理完成：成功{stats['updated']}，跳过{stats['skipped']}，失败{stats['failed']}"
        )
        print(
            f"总耗时: {elapsed_time:.1f}秒，平均每个产品 {elapsed_time/total_products:.2f}秒"
        )
        print("=" * 50)

    except Exception as e:
        error_msg = f"发生错误: {str(e)}"
        print(error_msg)
        print(traceback.format_exc())
        print("请确保您的API密钥和密码正确，并且有适当的API访问权限。")


if __name__ == "__main__":
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    main()
