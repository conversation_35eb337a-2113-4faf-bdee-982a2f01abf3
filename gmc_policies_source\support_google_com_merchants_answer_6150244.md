[Skip to main content](https://support.google.com/merchants/answer/6150244#hcfe-content)

# Editorial & professional requirements

This policy applies to Shopping ads and local inventory ads with merchant hosted local store front

## Our policy

|     |
| --- |
| In order to provide a quality customer experience, Google requires that all Shopping ads meet high professional and editorial standards. We only allow promotions that are clear and professional in appearance. These ads should lead customers to products and landing pages that are relevant, useful, and easy to interact with. |

## Technical requirements

The [product data specification](https://support.google.com/merchants/answer/188494) defines the structure and format for providing data about your products so that attractive and professional ads can be created. Your promotions will need to comply with the [product data specification](https://support.google.com/merchants/answer/188494) to appear in Shopping ads, so make sure to review the attributes and their requirements carefully.

* * *

## Examples of what's not allowed

| Website needs improvement |
| --- |

> ![](https://www.google.com/help/hc/images/no_good.gif)Websites that aren’t fully-functional, don’t have business specific content, have information that is difficult to understand, or display product content that is incomplete
>
> - **Examples**: A website has broken links, templated content, placeholders of text or placeholders for images.
>
> Learn [how to fix this issue](https://support.google.com/merchants/answer/********).

| Insufficient contact information |
| --- |

> ![](https://www.google.com/help/hc/images/no_good.gif) Websites that are missing required contact information, have unverified business information in Merchant Center, or both
>
> - **Examples**: Website is missing contact information (for example, no social media link, contact email address, or phone number); Merchant Center account has missing contact information, such as a physical business address or a verified phone number; contact information is missing from both the store website and Merchant Center account.

| Missing return and refund policy |
| --- |

> ![](https://www.google.com/help/hc/images/no_good.gif) Websites that are missing return and refund information
>
> - **Examples**: Store return policy pages that are empty or don't state all the requirements for return; refund policies that aren't clear or easy to find; no return or refund policy is clearly stated.

| Usefulness |
| --- |

> ![](https://www.google.com/help/hc/images/no_good.gif)Login is required or content is unnecessarily difficult or frustrating to navigate
>
> - **Examples**: Websites with pop-ups or interstitial ads that interfere with the customer’s ability to see the content requested; sites that require a visitor to enter a username or password to see the content requested; sites that disable or interfere with the browser’s back button; websites that don’t load quickly on most popular browsers and devices, are under construction, not functioning, require download of an additional application to view the landing page (aside from common browser plug-ins), or lead to a file, email address, or error messages. For specific guidelines on how to comply with this policy, refer to the 'link' attributes in the [product data specification](https://support.google.com/merchants/answer/188494)
>
> ![](https://www.google.com/help/hc/images/no_good.gif)Website that is not fully functional
>
> - **Examples**: Links for Contact Us information, social media business profiles, blogs, etc. on landing pages that don’t work and lead to an unrelated page. Ensure all links on the website are working and point to relevant pages.
>
> ![](https://www.google.com/help/hc/images/no_good.gif)Website that has incomplete or difficult to understand business information
>
> - **Examples**: Contact information, shipping information, or a frequently asked questions section on the website is incomplete or unclear for customers to take action on. Ensure that business information on your website is complete and easy to understand.
>
> ![](https://www.google.com/help/hc/images/no_good.gif)Not all customers are able to complete their purchase
>
> - **Examples**: Business information fields in your checkout process are required. Customers from certain internet locations (IP addresses) can not complete the checkout process.

| Spelling and grammar |
| --- |

> ![](https://www.google.com/help/hc/images/no_good.gif) Promotions that do not use commonly accepted spelling and grammar
>
> - **Examples**: "Flowers here buy" or "Buy flwres here"
>
> ![](https://www.google.com/help/hc/images/no_good.gif) Punctuation, symbols, capitalization, spacing, or repetition that are not used correctly or for their intended purpose
>
> - **Examples**: Excessive or gimmicky use of numbers, letters, symbols, punctuation, emoticons, emoji, repetition, or spacing, including: f1owers, fllllowers, fl@wers, Flowers!!!, f\*l\*o\*w\*e\*r\*s, FLOWERS, FlOwErS, F.L.O.W.E.R.S, flowers-flowers-flowers!, f l o w e r s, buyflowershere. For specific guidelines on how to comply with this policy, refer to the [title `[title]`](https://support.google.com/merchants/answer/6324415) and the [description `[description]`](https://support.google.com/merchants/answer/6324468) attributes in the [product data specification](https://support.google.com/merchants/answer/188494).

| Unclear relevance |
| --- |

> ![](https://www.google.com/help/hc/images/no_good.gif)Promotions that are not relevant to the landing page
>
> - **Examples**: Product title or description that is not relevant to the product promoted, generic landing pages or different product landing pages. For specific guidelines on how to comply with this policy, refer to the [title `[title]`](https://support.google.com/merchants/answer/6324415) and [description `[description]`](https://support.google.com/merchants/answer/6324468) attributes in the [Product data specification](https://support.google.com/merchants/answer/188494).

| Style requirements |
| --- |

> ![](https://www.google.com/help/hc/images/no_good.gif) Promotions that do not use the features of the Shopping ad for their intended purpose
>
> - _Examples_: Listings that use the title field as an additional description field; listings that display irrelevant or obtrusive information in images. For specific guidelines on how to comply with this policy, refer to the [title `[title]`](https://support.google.com/merchants/answer/6324415) and [description `[description]`](https://support.google.com/merchants/answer/6324468) attributes in the [Product data specification](https://support.google.com/merchants/answer/188494).
>
> ![](https://www.google.com/help/hc/images/no_good.gif)Website that is using generic placeholders or templated content
>
> - **Examples**: Generic text on the website that says “Add customer reviews and testimonials here” or an empty box that says “lorem ipsum.” Ensure text and images on the website are tailored to your business.

| Domain safety |
| --- |

> ![](https://www.google.com/help/hc/images/no_good.gif)Retailer landing pages or associated site domains that Google deems unsafe
>
> - **Examples**: Domain page deemed unsafe as a result of hacking or other activity causing the site to become compromised. If Google detects suspicious signals, it may block customers from visiting your site from some browsers and from Google Search.

| Product details |
| --- |

> ![](https://www.google.com/help/hc/images/no_good.gif)Website that has inaccurate, missing, or duplicate product details
>
> - **Examples**: Product descriptions and specifications that are unrelated to the product image or listing. The same product details being used for multiple products without differentiation. Ensure the product details on the website are accurate and unique for each product.

* * *

## What you can do

Here's what you can do if your product is disapproved or if your Merchant Center account is impacted:

Product disapproval

Products that don't comply with our policies may be disapproved. When a product is disapproved, it won't be eligible to serve.

1. **Read our policies to learn what we don't allow.**
2. **Update your website.** If your ad or listing leads to content that violates a policy, update your website to meet the requirements and be in compliance with our policies.
3. **Remove the violating products from your product data**. You’ll receive an email with details about the violation.;

   - If you have products in your product data that violate the policy (or policies), you'll need to remove the offers from your feed.
4. **Update your product data in Merchant Center**. If you created a schedule for automatic uploads, update your product data manually or wait for your product data to be automatically updated before requesting any reviews.
5. [**Request a review of your products or appeal the decision taken on this issue**.](https://support.google.com/merchants/answer/********)
   - If the violating products are removed, you won't need to request a review or take any additional action.
   - If you fix your product-level issues by editing your product data via your chosen upload method (such as a file) or directly in Merchant Center, your products will be re-reviewed.
   - For certain issues, you can disagree with the issue but you may need to complete additional steps such as provide an appeal reason and/or upload required documentation.
   - If you use a third-party platform to list your products, you can refer to your third-party if you want to request a review. You won’t be able to do this in Merchant Center.
   - If a review or appeal is successful, the issue will disappear from Merchant Center.

Account disapproval

For most violations, we'll send you a warning email detailing the policy violation and give you 7 or 28 calendar days to fix your issue. However, a warning may not be issued for egregious policy violations.

1. **Read our policies to learn what we don't allow.**
2. **Update your website.** If your ad or listing leads to content that violates a policy, update your website to meet the requirements and be in compliance with our policies.
3. **Remove any violating products from your product data**. You’ll receive an email with details about the violation. If you have products in your product data that violate the policy (or policies) you'll need to remove the offers from your feed.
4. **Update your data in Merchant Center**.

   - Review your account and submit any missing information or complete any unfinished steps.
   - If you created a schedule for automatic uploads, update your product data manually or wait for your product data to be automatically updated before requesting any reviews.
5. Make sure that your linked or associated account is issue-free before requesting a Merchant Center review. [Learn more about how to fix Linked account suspensions](https://support.google.com/merchants/answer/********).
6. [**Request a review of your account or appeal the decision taken on this issue**.](https://support.google.com/merchants/answer/********)
   - If your account is still within the warning period, it will automatically be reviewed again at the end of the warning period. If the violating products are removed, you won't need to request an account review or take any additional action.
   - If your account is suspended and you've taken actions to resolve the issue, request an account review.
   - For certain issues, you can disagree with the issue but you may need to complete additional steps such as provide an appeal reason and/or upload required documentation.
   - If you use a third-party platform to list your products, you can refer to your third-party if you want to request a review. You won’t be able to do this in Merchant Center.

**Note**: Account reviews typically take 7 business days, but may take longer for complex reviews. If you remove violating products, the warning will be removed. If a review or appeal is successful, the issue will disappear from Merchant Center. In the case of account suspension, we will approve the account and allow products to be displayed again.

Account issue that limits product visibility

Your account has an issue and is operating with reserved functionality, therefore your products have limited visibility. Check your email for a notice with details about the issue and steps to resolve it. Common issues include policy violations or missing account information.

1. **Read our policies to learn more about our requirements.**
2. **Update your website**. If your ad or listing leads to content that violates a policy, update your website to meet the requirements and be in compliance with our policies.
3. **Fix any other open policy violation in your account**. Review your products and your account details.

   - If products aren’t supported, remove them from your feed.
   - If account details are missing, complete them in Merchant Center.
4. [**Request a review of your account or appeal the decision taken on this issue**.](https://support.google.com/merchants/answer/********)
   - If your Merchant Center account is still within the warning period for certain policies, it will automatically be reviewed again at the end of the warning period.
   - If your account is affected and you've resolved the issue, request a Merchant Center account review.
   - For certain issues, you can disagree with the issue but you may need to complete additional steps such as provide an appeal reason and/or upload required documentation.
   - If you use a third-party platform to list your products, you can refer to your third-party if you want to request a review. You won’t be able to do this in Merchant Center.

**Note**: Account reviews typically take 7 business days, but may take longer for complex reviews. If you remove violating products, the warning will be removed. If a review or appeal is successful, the issue will disappear from Merchant Center. In the case of account suspension, we will approve the account and allow products to be displayed again.

To ensure a safe and positive experience for customers, Google requires that retailers comply with all applicable laws and regulations in addition to [our policies](https://support.google.com/merchants/answer/6149970). It's important that you familiarize yourself with and keep up to date on these requirements for the place where your business operates, as well as any other places your ads are shown. When we find content that violates these requirements, we may block it from appearing. In cases of repeated or egregious violations, we may ban you from advertising content with us.

If you’re working with a third-party platform, some of these instructions may not apply to you. Refer to your third-party platform for instructions on how to resolve the issue and request a review. [Learn how to find support if you use a non-Google platform](https://support.google.com/merchants/answer/********).

Give feedback about this article

Choose a section to give feedback on

## Was this helpful?

How can we improve it?

YesNo

Submit

## Need more help?

### Try these next steps:

[Post to the help community  Get answers from community members](https://support.google.com/google-ads/threads?thread_filter=(category%3Agoogle_ads_google_shopping_merchant_center)) [Contact usTell us more and we’ll help you get there](https://support.google.com/merchants/gethelp)

true

16109868021044670563

true

Search

Clear search

Close search

Main menu

Google apps

Search Help Center

true

true

true

[Google Help](https://support.google.com/)

[Help Center](https://support.google.com/merchants/?hl=en)

[Get to know Merchant Center](https://support.google.com/merchants/topic/12158920?hl=en&ref_topic=7259405,) [Business settings](https://support.google.com/merchants/topic/11983022?hl=en&ref_topic=7259405,) [Upload your products](https://support.google.com/merchants/topic/11983731?hl=en&ref_topic=7259405,) [Product data spec](https://support.google.com/merchants/topic/7259406?hl=en&ref_topic=7259405,) [Market your products](https://support.google.com/merchants/topic/12500201?hl=en&ref_topic=7259405,) [Add-ons and additional features](https://support.google.com/merchants/topic/********?hl=en&ref_topic=7259405,) [Understand your performance](https://support.google.com/merchants/topic/11983633?hl=en&ref_topic=7259405,) [Troubleshoot](https://support.google.com/merchants/topic/********?hl=en&ref_topic=7259405,) [3rd party platform integrations](https://support.google.com/merchants/topic/********?hl=en&ref_topic=7259405,)

[Google Merchant Center](https://merchants.google.com/)

[Privacy Policy](https://www.google.com/intl/en/privacy.html) [Terms of Service](https://support.google.com/merchants/answer/160173)Submit feedback

[Get started](https://support.google.com/merchants/topic/15302229?hl=en&ref_topic=12158920,7259405,) [I need help with set up](https://support.google.com/merchants/topic/12671531?hl=en&ref_topic=12158920,7259405,) [Comparison Shopping Services](https://support.google.com/merchants/topic/12652782?hl=en&ref_topic=12158920,7259405,) [Policies and requirements](https://support.google.com/merchants/topic/7286989?hl=en&ref_topic=12158920,7259405,) [Glossary](https://support.google.com/merchants/table/15620279?hl=en&ref_topic=12158920,7259405,)

[Manage your sales tax settings](https://support.google.com/merchants/topic/12671521?hl=en&ref_topic=11983022,7259405,) [Manage your shipping settings](https://support.google.com/merchants/topic/12570808?hl=en&ref_topic=11983022,7259405,) [Manage your business settings](https://support.google.com/merchants/topic/12564660?hl=en&ref_topic=11983022,7259405,) [Manage integrations](https://support.google.com/merchants/topic/12564658?hl=en&ref_topic=11983022,7259405,)

[Add products](https://support.google.com/merchants/topic/12672304?hl=en&ref_topic=11983731,7259405,) [Maintain your product data](https://support.google.com/merchants/topic/12672214?hl=en&ref_topic=11983731,7259405,) [Set up rules for your product data sources](https://support.google.com/merchants/topic/14957981?hl=en&ref_topic=11983731,7259405,) [Create product data sources for Performance Max campaigns](https://support.google.com/merchants/topic/14958782?hl=en&ref_topic=11983731,7259405,) [Use AI and AR tools to help enhance product images](https://support.google.com/merchants/topic/15189223?hl=en&ref_topic=11983731,7259405,) [Troubleshoot product data source issues](https://support.google.com/merchants/topic/14957024?hl=en&ref_topic=11983731,7259405,) [Product data specifications](https://support.google.com/merchants/topic/14963864?hl=en&ref_topic=11983731,7259405,) [Best practices for different product situations](https://support.google.com/merchants/topic/15712169?hl=en&ref_topic=11983731,7259405,)

[Ad campaigns](https://support.google.com/merchants/topic/15191542?hl=en&ref_topic=12500201,7259405,) [Automated discounts](https://support.google.com/merchants/topic/15303099?hl=en&ref_topic=12500201,7259405,) [Free listings](https://support.google.com/merchants/topic/15190028?hl=en&ref_topic=12500201,7259405,) [Local inventory ads and free local listings](https://support.google.com/merchants/topic/14607119?hl=en&ref_topic=12500201,7259405,) [Prepare for big events](https://support.google.com/merchants/topic/15710958?hl=en&ref_topic=12500201,7259405,) [YouTube Shopping affiliate program](https://support.google.com/merchants/topic/14813437?hl=en&ref_topic=12500201,7259405,) [Promotions](https://support.google.com/merchants/topic/********?hl=en&ref_topic=12500201,7259405,) [YouTube Store](https://support.google.com/merchants/topic/15643752?hl=en&ref_topic=12500201,7259405,) [Vehicle ads](https://support.google.com/merchants/topic/********?hl=en&ref_topic=12500201,7259405,)

[Automated discounts](https://support.google.com/merchants/topic/15303099?hl=en&ref_topic=********,7259405,) [Google Customer Reviews](https://support.google.com/merchants/topic/14628801?hl=en&ref_topic=********,7259405,) [Local inventory ads and free local listings](https://support.google.com/merchants/topic/14607119?hl=en&ref_topic=********,7259405,) [Loyalty programs](https://support.google.com/merchants/topic/15164622?hl=en&ref_topic=********,7259405,) [Product Ratings](https://support.google.com/merchants/topic/14548703?hl=en&ref_topic=********,7259405,) [Product Studio](https://support.google.com/merchants/topic/********?hl=en&ref_topic=********,7259405,) [Promotions](https://support.google.com/merchants/topic/********?hl=en&ref_topic=********,7259405,) [Store Ratings](https://support.google.com/merchants/topic/********?hl=en&ref_topic=********,7259405,) [Vehicle ads](https://support.google.com/merchants/topic/********?hl=en&ref_topic=********,7259405,)

[About issues](https://support.google.com/merchants/topic/********?hl=en&ref_topic=********,7259405,)

[Manage your account with a Shopify integration](https://support.google.com/merchants/topic/********?hl=en&ref_topic=********,7259405,)

true

true

71525

false

false

## What is the issue with this selection?

Inaccurate - doesn't match what I see in the product

Hard to understand - unclear or translation is wrong

Missing info - relevant but not comprehensive

Irrelevant - doesn’t match the title and / or my expectations

Minor errors - formatting issues, typos, and / or broken links

Other suggestions - ideas to improve the content

## Share additional info or suggestions

​

​

Do not share any personal info

Cancel

Submit

By continuing, you agree Google uses your answers, [account & system info](https://support.google.com/merchants/answer/6150244#) to improve services, per our [Privacy](https://myaccount.google.com/privacypolicy?hl=en) & [Terms](https://policies.google.com/terms?hl=en).

false

false