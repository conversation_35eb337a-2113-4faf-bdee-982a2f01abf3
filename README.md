# 谷歌购物广告政策分析工具

这个工具集用于处理谷歌购物广告政策数据，将其分割成小块并存入向量数据库(ChromaDB)中，以便后续使用AI大模型进行分析和查询。

## 功能特点

- 从Markdown文件中提取谷歌购物广告政策内容
- 智能分块：按标题层级和内容长度进行合理分割
- 保留元数据：包括政策类型、标题结构和源URL
- 向量化存储：使用多语言句向量模型进行文本嵌入
- 语义搜索：基于语义相似度而非关键词匹配进行查询

## 安装说明

1. 安装所需的Python依赖：

```bash
pip install -r requirements.txt
```

2. 确保已安装Playwright浏览器：

```bash
playwright install
```

## 文件说明

- `policy_to_chromadb.py`: 将政策文档分割并存入ChromaDB的主脚本
- `query_policy_db.py`: 用于查询向量数据库中的政策内容
- `gmc_policies_source/`: 包含从谷歌购物广告政策页面采集的源数据MD文档

## 使用方法

### 步骤1：处理政策文档并创建向量数据库

运行以下命令将政策文档分割并存入ChromaDB：

```bash
python policy_to_chromadb.py
```

这个脚本会：
- 读取`gmc_policies_source`目录下的所有Markdown文件
- 将文档按标题分块，并进一步处理大块文本
- 提取文件元数据和标题结构信息
- 将分块后的内容存入ChromaDB

### 步骤2：查询政策内容

运行以下命令以交互方式查询政策内容：

```bash
python query_policy_db.py
```

这个工具提供了一个交互式界面：
- 输入查询内容（例如："关于仿冒商品的政策"）
- 选择是否显示完整内容
- 指定返回结果的数量
- 查看与查询语义相关的政策内容

## 数据结构

政策文档被分割成多个小块，每个块都包含以下信息：

- **文本内容**: 政策的实际内容
- **元数据**:
  - 来源文件名
  - 政策类型（answer/topic）
  - 政策ID
  - 源URL
  - 标题层级（标题1-4）
  - 块大小信息

## 后续应用

这个向量数据库可以用于：

1. 与大语言模型(LLM)集成，进行政策符合性分析
2. 开发自动审核系统，检查商品描述是否符合谷歌政策
3. 构建问答系统，解答用户关于谷歌购物广告政策的疑问
4. 分析政策变更和趋势

## 故障排除

- 如果遇到模块导入错误，请确认已安装所有依赖
- 如果ChromaDB初始化失败，请检查是否有足够的磁盘空间
- 如果分块结果不理想，可以调整`CHUNK_SIZE`和`CHUNK_OVERLAP`参数 