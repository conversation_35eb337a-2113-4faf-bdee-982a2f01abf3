2025-06-02 18:45:16 [INFO] 日志已初始化，日志文件：logs/policy_pilot_20250602_184516.log
2025-06-02 18:45:16 [INFO] 加载配置完成
2025-06-02 18:45:16 [INFO] ============================================================
2025-06-02 18:45:16 [INFO] Shopify商品合规性分析及飞书集成工具启动
2025-06-02 18:45:16 [INFO] ============================================================
2025-06-02 18:45:16 [INFO] 初始化ChromaDB...
2025-06-02 18:45:21 [INFO] 配置设置为强制使用CUDA进行嵌入计算
2025-06-02 18:45:21 [INFO] ===== PyTorch CUDA 安装检查 =====
2025-06-02 18:45:21 [INFO] Python版本: 3.11.9
2025-06-02 18:45:21 [INFO] PyTorch版本: 2.7.0+cpu
2025-06-02 18:45:21 [INFO] CUDA可用性: False
2025-06-02 18:45:21 [WARNING] CUDA不可用，检查可能的原因...
2025-06-02 18:45:21 [WARNING] PyTorch版本可能不包含CUDA支持，考虑重新安装支持CUDA的PyTorch版本
2025-06-02 18:45:21 [INFO] CUDA_HOME环境变量: C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.4
2025-06-02 18:45:21 [INFO] CUDA在系统PATH中: True
2025-06-02 18:45:21 [INFO] NVCC版本信息: nvcc: NVIDIA (R) Cuda compiler driver
Copyright (c) 2005-2024 NVIDIA Corporation
Built on Thu_Mar_28_02:30:10_Pacific_Daylight_Time_2024
Cuda compilation tools, release 12.4, V12.4.131
Build cuda_12.4.r12.4/compiler.34097967_0
2025-06-02 18:45:21 [INFO] NVIDIA驱动已安装，nvidia-smi输出:
2025-06-02 18:45:21 [INFO] Mon Jun  2 18:45:21 2025       
2025-06-02 18:45:21 [INFO] +-----------------------------------------------------------------------------------------+
2025-06-02 18:45:21 [INFO] | NVIDIA-SMI 572.47                 Driver Version: 572.47         CUDA Version: 12.8     |
2025-06-02 18:45:21 [INFO] |-----------------------------------------+------------------------+----------------------+
2025-06-02 18:45:21 [INFO] | GPU  Name                  Driver-Model | Bus-Id          Disp.A | Volatile Uncorr. ECC |
2025-06-02 18:45:21 [INFO] | Fan  Temp   Perf          Pwr:Usage/Cap |           Memory-Usage | GPU-Util  Compute M. |
2025-06-02 18:45:21 [INFO] |                                         |                        |               MIG M. |
2025-06-02 18:45:21 [INFO] |=========================================+========================+======================|
2025-06-02 18:45:21 [INFO] |   0  NVIDIA GeForce RTX 3060 Ti   WDDM  |   00000000:01:00.0  On |                  N/A |
2025-06-02 18:45:21 [INFO] | 30%   44C    P8             23W /  220W |     960MiB /   8192MiB |      3%      Default |
2025-06-02 18:45:21 [INFO] ===== PyTorch CUDA 检查完成 =====
2025-06-02 18:45:21 [WARNING] 强制使用CUDA模式，但CUDA不可用
2025-06-02 18:45:21 [WARNING] PyTorch可能未安装CUDA支持版本或CUDA驱动有问题
2025-06-02 18:45:21 [WARNING] 回退到CPU模式
2025-06-02 18:45:21 [INFO] 开始加载SentenceTransformer模型: paraphrase-multilingual-mpnet-base-v2
2025-06-02 18:45:21 [INFO] 尝试方法1: 直接加载模型到cpu设备
2025-06-02 18:45:21 [INFO] Load pretrained SentenceTransformer: paraphrase-multilingual-mpnet-base-v2
2025-06-02 18:45:27 [INFO] 方法1成功: 模型加载完成，使用设备: cpu
2025-06-02 18:45:27 [INFO] 成功创建嵌入函数: paraphrase-multilingual-mpnet-base-v2
2025-06-02 18:45:27 [INFO] 成功初始化嵌入函数
2025-06-02 18:45:27 [INFO] Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-06-02 18:45:27 [ERROR] 获取或创建集合失败: no such column: collections.topic
2025-06-02 18:45:27 [ERROR] 获取现有集合失败: no such column: collections.topic
2025-06-02 18:45:27 [ERROR] ChromaDB初始化失败: no such column: collections.topic
2025-06-02 18:45:27 [ERROR] ChromaDB初始化失败: no such column: collections.topic
2025-06-02 18:45:27 [INFO] 开始第一部分：商品合规性分析
