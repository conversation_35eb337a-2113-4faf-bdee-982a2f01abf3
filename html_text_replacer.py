"""
HTML AST 精准文本替换模块
保留所有HTML结构，只替换文本内容，不破坏标签嵌套
"""

from bs4 import BeautifulSoup, NavigableString
import re
from html import unescape


class HTMLTextReplacer:
    """HTML文本精准替换器"""

    def __init__(self):
        self.debug = False

    def set_debug(self, debug=True):
        """设置调试模式"""
        self.debug = debug

    def extract_text_with_positions(self, soup):
        """
        提取HTML中的所有文本内容及其在DOM中的位置
        返回: [(text_content, text_node, start_pos, end_pos), ...]
        """
        text_segments = []
        current_pos = 0

        def traverse_node(node):
            nonlocal current_pos

            if isinstance(node, NavigableString):
                # 这是一个文本节点
                text_content = str(node)
                if text_content.strip():  # 只处理非空文本
                    start_pos = current_pos
                    end_pos = current_pos + len(text_content)
                    text_segments.append((text_content, node, start_pos, end_pos))
                    current_pos = end_pos
            else:
                # 这是一个标签节点，递归处理其子节点
                for child in node.children:
                    traverse_node(child)

        traverse_node(soup)
        return text_segments

    def build_flat_text(self, text_segments):
        """
        构建扁平化的文本字符串
        返回: (flat_text, position_map)
        """
        flat_text = ""
        position_map = (
            []
        )  # [(flat_start, flat_end, text_node, original_start, original_end)]

        flat_pos = 0
        for text_content, text_node, start_pos, end_pos in text_segments:
            # 标准化文本内容：处理换行符和多余空格
            normalized_content = re.sub(r"\s+", " ", text_content)

            flat_start = flat_pos
            flat_end = flat_pos + len(normalized_content)
            flat_text += normalized_content
            position_map.append((flat_start, flat_end, text_node, start_pos, end_pos))
            flat_pos = flat_end

        return flat_text, position_map

    def normalize_text_for_matching(self, text):
        """
        标准化文本用于匹配：处理空格、换行符、HTML实体等
        """
        # 解码HTML实体
        from html import unescape

        text = unescape(text)

        # 标准化空白字符（换行符、制表符、多个空格等都变成单个空格）
        text = re.sub(r"\s+", " ", text)

        # 去除首尾空格
        text = text.strip()

        return text

    def find_text_matches(self, flat_text, search_text):
        """
        在扁平化文本中查找所有匹配位置
        返回: [(match_start, match_end), ...]
        """
        matches = []

        # 标准化搜索文本和目标文本
        normalized_flat_text = self.normalize_text_for_matching(flat_text)
        normalized_search_text = self.normalize_text_for_matching(search_text)

        if self.debug:
            print(f"[DEBUG] 标准化后的扁平文本: '{normalized_flat_text}'")
            print(f"[DEBUG] 标准化后的搜索文本: '{normalized_search_text}'")

        start = 0
        while True:
            pos = normalized_flat_text.find(normalized_search_text, start)
            if pos == -1:
                break
            matches.append((pos, pos + len(normalized_search_text)))
            start = pos + 1  # 允许重叠匹配

        return matches

    def map_matches_to_nodes(self, matches, position_map):
        """
        将扁平文本中的匹配位置映射回DOM节点
        返回: [[(node, node_start, node_end, match_text), ...], ...]
        """
        match_mappings = []

        for match_start, match_end in matches:
            node_mappings = []

            for flat_start, flat_end, text_node, orig_start, orig_end in position_map:
                # 检查这个文本节点是否与当前匹配有重叠
                overlap_start = max(match_start, flat_start)
                overlap_end = min(match_end, flat_end)

                if overlap_start < overlap_end:
                    # 有重叠，计算在节点内的相对位置
                    node_start = overlap_start - flat_start
                    node_end = overlap_end - flat_start
                    match_text = str(text_node)[node_start:node_end]
                    node_mappings.append((text_node, node_start, node_end, match_text))

            if node_mappings:
                match_mappings.append(node_mappings)

        return match_mappings

    def replace_in_nodes(self, match_mappings, old_text, new_text):
        """
        在DOM节点中执行实际的文本替换
        """
        replaced_count = 0

        # 需要跟踪已经处理过的节点，避免重复替换
        processed_nodes = set()

        for match_index, node_mappings in enumerate(match_mappings):
            if self.debug:
                print(f"[DEBUG] 处理匹配 {match_index + 1}")

            # 计算这个匹配跨越的总文本长度
            total_match_text = ""
            for text_node, node_start, node_end, match_text in node_mappings:
                total_match_text += match_text

            if self.debug:
                print(f"[DEBUG] 完整匹配文本: '{total_match_text}'")

            # 只处理第一个节点，将整个匹配的新文本放在这里
            # 其他节点中匹配的部分删除
            for i, (text_node, node_start, node_end, match_text) in enumerate(
                node_mappings
            ):
                node_id = id(text_node)

                if node_id in processed_nodes:
                    continue

                try:
                    original_text = str(text_node)

                    if self.debug:
                        print(f"[DEBUG] 节点{i} 原文: '{original_text}'")
                        print(
                            f"[DEBUG] 节点{i} 匹配部分: '{match_text}' (位置: {node_start}-{node_end})"
                        )

                    if i == 0:
                        # 第一个节点：替换匹配部分为新文本
                        before = original_text[:node_start]
                        after = original_text[node_end:]
                        if new_text == "":
                            new_node_text = before + after
                        else:
                            new_node_text = before + new_text + after
                    else:
                        # 其他节点：删除匹配部分
                        before = original_text[:node_start]
                        after = original_text[node_end:]
                        new_node_text = before + after

                    # 替换节点内容
                    if new_node_text != original_text:
                        text_node.replace_with(new_node_text)
                        processed_nodes.add(node_id)
                        replaced_count += 1

                        if self.debug:
                            print(f"[DEBUG] 节点{i} 新文: '{new_node_text}'")
                    else:
                        if self.debug:
                            print(f"[DEBUG] 节点{i} 无需更改")

                except Exception as e:
                    if self.debug:
                        print(f"[DEBUG] 替换节点时出错: {e}")
                    continue

        return replaced_count

    def replace_text_preserve_html(self, html_content, old_text, new_text, debug=False):
        """
        在HTML中替换文本，完全保留HTML结构

        参数:
            html_content (str): 原始HTML内容
            old_text (str): 要替换的文本
            new_text (str): 新文本
            debug (bool): 是否启用调试模式

        返回:
            tuple: (new_html_content, replaced_count)
        """
        self.debug = debug

        if not html_content or not old_text:
            return html_content, 0

        try:
            # 解析HTML
            soup = BeautifulSoup(html_content, "html.parser")

            if self.debug:
                print(f"[DEBUG] 原始HTML: {html_content}")
                print(f"[DEBUG] 查找文本: '{old_text}'")
                print(f"[DEBUG] 替换为: '{new_text}'")

            # 提取文本段落及位置
            text_segments = self.extract_text_with_positions(soup)

            if self.debug:
                print(f"[DEBUG] 找到 {len(text_segments)} 个文本段落")
                for i, (text, node, start, end) in enumerate(text_segments):
                    print(f"[DEBUG] 段落{i}: '{text}' (位置: {start}-{end})")

            # 构建扁平化文本
            flat_text, position_map = self.build_flat_text(text_segments)

            if self.debug:
                print(f"[DEBUG] 扁平化文本: '{flat_text}'")

            # 查找匹配
            matches = self.find_text_matches(flat_text, old_text)

            if self.debug:
                print(f"[DEBUG] 找到 {len(matches)} 个匹配")
                for i, (start, end) in enumerate(matches):
                    print(
                        f"[DEBUG] 匹配{i}: 位置 {start}-{end}, 内容: '{flat_text[start:end]}'"
                    )

            if not matches:
                return html_content, 0

            # 映射匹配到DOM节点
            match_mappings = self.map_matches_to_nodes(matches, position_map)

            if self.debug:
                print(f"[DEBUG] 映射到 {len(match_mappings)} 个节点组")

            # 执行替换
            replaced_count = self.replace_in_nodes(match_mappings, old_text, new_text)

            # 生成新的HTML
            new_html = str(soup)

            # 清理BeautifulSoup添加的额外标签
            if new_html.startswith("<html><body>") and new_html.endswith(
                "</body></html>"
            ):
                new_html = new_html[12:-14]  # 移除<html><body>和</body></html>

            # 清理空的HTML标签
            import re

            # 移除空的标签（但保留自闭合标签如<br>, <img>等）
            empty_tag_pattern = r"<(?!(?:area|base|br|col|embed|hr|img|input|link|meta|param|source|track|wbr)\b)([a-zA-Z][a-zA-Z0-9]*)\b[^>]*>\s*</\1>"
            while re.search(empty_tag_pattern, new_html):
                new_html = re.sub(empty_tag_pattern, "", new_html)

            # 清理多余的空白字符
            new_html = re.sub(r"\s+", " ", new_html).strip()

            if self.debug:
                print(f"[DEBUG] 新HTML: {new_html}")
                print(f"[DEBUG] 替换了 {replaced_count} 处")

            return new_html, replaced_count

        except Exception as e:
            if self.debug:
                print(f"[DEBUG] HTML解析或替换时出错: {e}")
            return html_content, 0


def smart_text_replace(html_content, old_text, new_text, debug=False):
    """
    智能文本替换函数 - 保留HTML结构的精准文本替换

    参数:
        html_content (str): 原始HTML内容
        old_text (str): 要替换的文本
        new_text (str): 新文本（空字符串表示删除）
        debug (bool): 是否启用调试模式

    返回:
        tuple: (new_html_content, replaced_count)
    """
    replacer = HTMLTextReplacer()
    return replacer.replace_text_preserve_html(html_content, old_text, new_text, debug)


# 测试函数
def test_html_replacer():
    """测试HTML替换功能"""
    test_cases = [
        {
            "html": "<p>Hello <strong>world</strong> and <em>universe</em>!</p>",
            "old": "world and universe",
            "new": "beautiful galaxy",
            "expected": "<p>Hello <strong>beautiful</strong> <em>galaxy</em>!</p>",
        },
        {
            "html": "<div>: Listed with <strong>UL, ETL</strong>, CE, <em>SAA</em>, and CSA.</div>",
            "old": ": Listed with UL, ETL, CE, SAA, and CSA.",
            "new": "Certifications: UL, CE, SAA listed",
            "expected": "<div>Certifications: <strong>UL,</strong> <em>CE,</em> SAA listed</div>",
        },
    ]

    replacer = HTMLTextReplacer()
    replacer.set_debug(True)

    for i, case in enumerate(test_cases):
        print(f"\n=== 测试案例 {i+1} ===")
        result, count = replacer.replace_text_preserve_html(
            case["html"], case["old"], case["new"], debug=True
        )
        print(f"结果: {result}")
        print(f"替换次数: {count}")


if __name__ == "__main__":
    test_html_replacer()
