import requests
import json
import time
import threading
import concurrent.futures
import base64


def sync_media_alt_for_products(
    products, config, rate_limiter, print_lock, result_collector, max_workers=2
):
    """
    批量同步产品图片的alt值为产品标题

    参数:
        products (list): 产品列表
        config (dict): 配置信息
        rate_limiter: 速率限制器
        print_lock: 打印锁
        result_collector: 结果收集器
        max_workers (int): 最大线程数
    """

    def get_auth_headers():
        """生成Basic认证头"""
        api_key = config["shopify"]["api_key"]
        api_password = config["shopify"]["api_password"]
        auth_str = f"{api_key}:{api_password}"
        auth_bytes = auth_str.encode("ascii")
        auth_b64_bytes = base64.b64encode(auth_bytes)
        auth_b64_str = auth_b64_bytes.decode("ascii")
        return {
            "Content-Type": "application/json",
            "Authorization": f"Basic {auth_b64_str}",
        }

    def get_product_gid(product_id):
        return f"gid://shopify/Product/{product_id}"

    def get_product_images(product_id):
        """获取产品的图片信息 - 使用REST API"""
        shop_name = config["shopify"]["shop_name"]
        url = f"https://{shop_name}.myshopify.com/admin/api/2023-04/products/{product_id}/images.json"
        headers = get_auth_headers()

        rate_limiter.wait_if_needed()
        try:
            resp = requests.get(url, headers=headers, verify=False, timeout=20)
            if resp.status_code == 200:
                data = resp.json()
                if data and "images" in data:
                    return data["images"]
                else:
                    with print_lock:
                        print(
                            f"[WARNING] 获取产品{product_id}图片信息失败: 数据格式错误"
                        )
            else:
                with print_lock:
                    print(
                        f"[WARNING] 获取产品{product_id}图片信息失败: {resp.status_code} - {resp.text}"
                    )
        except Exception as e:
            with print_lock:
                print(f"[WARNING] 获取产品{product_id}图片信息失败: {e}")
        return []

    def update_media_alt(media_id, alt_text, product_gid, max_retries=3):
        """更新媒体文件的alt值 - 使用REST API"""
        # 从media_id中提取实际的图片ID
        # media_id格式: gid://shopify/MediaImage/40475756560675
        image_id = media_id.split("/")[-1]
        product_id = product_gid.split("/")[-1]

        shop_name = config["shopify"]["shop_name"]
        # 使用REST API更新产品图片
        url = f"https://{shop_name}.myshopify.com/admin/api/2023-04/products/{product_id}/images/{image_id}.json"
        headers = get_auth_headers()
        headers["Content-Type"] = "application/json"

        data = {"image": {"id": int(image_id), "alt": alt_text}}

        for attempt in range(max_retries):
            rate_limiter.wait_if_needed()
            try:
                resp = requests.put(
                    url, headers=headers, json=data, verify=False, timeout=10
                )
                if resp.status_code == 200:
                    response_data = resp.json()
                    if response_data and "image" in response_data:
                        return True
                    else:
                        if attempt < max_retries - 1:
                            with print_lock:
                                print(
                                    f"[WARNING] 图片{image_id}更新返回数据格式错误，重试 {attempt + 1}/{max_retries}"
                                )
                            time.sleep(3 + (2**attempt))
                            continue
                        else:
                            with print_lock:
                                print(f"[ERROR] 图片{image_id}更新返回数据格式错误")
                            return False
                elif resp.status_code == 404:
                    with print_lock:
                        print(f"[ERROR] 图片{image_id}不存在或已删除")
                    return False
                elif resp.status_code == 422:
                    with print_lock:
                        print(f"[ERROR] 图片{image_id}更新参数错误: {resp.text}")
                    return False
                elif resp.status_code in [429, 500, 502, 503, 504]:
                    # 可重试的错误
                    if attempt < max_retries - 1:
                        with print_lock:
                            print(
                                f"[WARNING] 图片{image_id} HTTP错误 {resp.status_code}，重试 {attempt + 1}/{max_retries}"
                            )
                        time.sleep(3 + (2**attempt))
                        continue
                    else:
                        with print_lock:
                            print(
                                f"[ERROR] 图片{image_id} REST API更新失败: {resp.status_code} - {resp.text}"
                            )
                        return False
                else:
                    with print_lock:
                        print(
                            f"[ERROR] 图片{image_id} REST API更新失败: {resp.status_code} - {resp.text}"
                        )
                    return False
            except Exception as e:
                if attempt < max_retries - 1:
                    with print_lock:
                        print(
                            f"[WARNING] 图片{image_id} 请求异常，重试 {attempt + 1}/{max_retries}: {e}"
                        )
                    time.sleep(3 + (2**attempt))
                    continue
                else:
                    with print_lock:
                        print(f"[ERROR] 图片{image_id} REST API请求异常: {e}")
                    return False

        return False

    def sync_single_product_media_alt(product, product_index=None, total_products=None):
        """
        同步单个产品的媒体alt值

        参数:
            product (dict): 产品信息
            product_index (int): 产品索引
            total_products (int): 总产品数

        返回:
            tuple: (result_type, product_info)
        """
        try:
            product_id = product["id"]
            product_gid = get_product_gid(product_id)

            # 获取产品标题
            title = product.get("title", "")
            if not title:
                # 如果产品对象中没有标题，尝试从API获取
                shop_name = config["shopify"]["shop_name"]
                url = f"https://{shop_name}.myshopify.com/admin/api/2023-04/products/{product_id}.json"
                headers = get_auth_headers()
                rate_limiter.wait_if_needed()
                resp = requests.get(url, headers=headers, verify=False, timeout=10)
                if resp.status_code == 200:
                    product_detail = resp.json().get("product", {})
                    title = product_detail.get("title", "")

            if not title:
                with print_lock:
                    print(f"产品{product_id}没有标题，跳过")
                return "skipped", {"id": product_id, "title": "无标题"}

            # 获取产品图片信息
            images = get_product_images(product_id)
            if not images:
                with print_lock:
                    print(f"产品{product_id}没有图片，跳过")
                return "skipped", {"id": product_id, "title": title}

            # 统计需要更新的图片
            images_to_update = []
            for image in images:
                image_id = image.get("id")
                current_alt = image.get("alt", "")

                if image_id and current_alt != title:
                    images_to_update.append(
                        {
                            "image_id": image_id,
                            "current_alt": current_alt,
                            "new_alt": title,
                        }
                    )

            if not images_to_update:
                with print_lock:
                    print(
                        f"- 产品 {title}（ID: {product_id}）的图片alt值已是最新，无需更新"
                    )
                return "skipped", {"id": product_id, "title": title}

            # 更新图片alt值
            success_count = 0
            total_images = len(images_to_update)

            for image_info in images_to_update:
                image_id = image_info["image_id"]
                # 构造一个假的media_id用于update_media_alt函数
                fake_media_id = f"gid://shopify/MediaImage/{image_id}"

                success = update_media_alt(
                    fake_media_id, image_info["new_alt"], product_gid
                )
                if success:
                    success_count += 1
                else:
                    with print_lock:
                        print(f"× 图片{image_id}更新失败")

            if success_count == total_images:
                # 生成产品链接
                custom_domain = config["shopify"].get("custom_domain", "")
                shop_name = config["shopify"]["shop_name"]

                # 获取产品handle
                shop_url = f"https://{shop_name}.myshopify.com/admin/api/2023-04/products/{product_id}.json"
                headers = get_auth_headers()
                rate_limiter.wait_if_needed()
                resp = requests.get(shop_url, headers=headers, verify=False, timeout=10)
                product_handle = ""
                if resp.status_code == 200:
                    product_detail = resp.json().get("product", {})
                    product_handle = product_detail.get("handle", "")

                product_link = (
                    f"https://{custom_domain}/products/{product_handle}"
                    if custom_domain and product_handle
                    else f"https://{shop_name}.myshopify.com/products/{product_handle}"
                )

                # 显示进度信息
                progress_info = ""
                if product_index is not None and total_products is not None:
                    progress_info = f" - 进度: {product_index+1}/{total_products}"

                with print_lock:
                    print(
                        f"✓ 产品 {title}（ID: {product_id}）的{total_images}个图片alt值已更新{progress_info}"
                    )
                    if product_handle:
                        print(f"  产品链接: {product_link}")

                return "updated", {
                    "id": product_id,
                    "title": title,
                    "link": product_link if product_handle else "",
                    "media_count": total_images,
                }
            elif success_count > 0:
                with print_lock:
                    print(
                        f"⚠ 产品 {title}（ID: {product_id}）部分图片alt值更新成功（{success_count}/{total_images}）"
                    )
                return "updated", {
                    "id": product_id,
                    "title": title,
                    "media_count": success_count,
                }
            else:
                with print_lock:
                    print(f"× 产品 {title}（ID: {product_id}）图片alt值更新失败")
                return "failed", {"id": product_id, "title": title}

        except Exception as e:
            with print_lock:
                print(f"处理产品媒体alt同步时发生错误: {str(e)}")
            return "failed", {
                "id": product.get("id", "unknown"),
                "title": product.get("title", "unknown"),
            }

    # 执行批量处理
    total_products = len(products)
    print(f"开始同步 {total_products} 个产品的图片alt值...")

    start_time = time.time()

    if max_workers > 1:
        # 多线程模式
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_index = {}
            for i, product in enumerate(products):
                future = executor.submit(
                    sync_single_product_media_alt, product, i, total_products
                )
                future_to_index[future] = i

            # 处理完成的任务
            for future in concurrent.futures.as_completed(future_to_index):
                try:
                    result_type, product_info = future.result()
                    result_collector.add_result(result_type, product_info)

                    # 定期显示进度
                    stats = result_collector.get_stats()
                    if (stats["total"] % 10 == 0) or (stats["total"] == total_products):
                        elapsed = time.time() - start_time
                        with print_lock:
                            print(
                                f"\n当前进度: {stats['total']}/{total_products} "
                                f"(成功: {stats['updated']}, 跳过: {stats['skipped']}, "
                                f"失败: {stats['failed']}) - 耗时: {elapsed:.1f}秒"
                            )
                except Exception as e:
                    with print_lock:
                        print(f"处理媒体alt同步任务时发生错误: {str(e)}")
                    result_collector.add_result("failed")
    else:
        # 单线程模式
        for i, product in enumerate(products):
            result_type, product_info = sync_single_product_media_alt(
                product, i, total_products
            )
            result_collector.add_result(result_type, product_info)
