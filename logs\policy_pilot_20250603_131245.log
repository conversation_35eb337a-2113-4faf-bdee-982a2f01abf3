2025-06-03 13:12:45 [INFO] 日志已初始化，日志文件：logs/policy_pilot_20250603_131245.log
2025-06-03 13:12:45 [INFO] 加载配置完成
2025-06-03 13:12:45 [INFO] ============================================================
2025-06-03 13:12:45 [INFO] Shopify商品合规性分析及飞书集成工具启动
2025-06-03 13:12:45 [INFO] ============================================================
2025-06-03 13:12:45 [INFO] 初始化ChromaDB...
2025-06-03 13:12:51 [INFO] 配置设置为强制使用CUDA进行嵌入计算
2025-06-03 13:12:51 [INFO] ===== PyTorch CUDA 安装检查 =====
2025-06-03 13:12:51 [INFO] Python版本: 3.11.9
2025-06-03 13:12:51 [INFO] PyTorch版本: 2.6.0+cu124
2025-06-03 13:12:51 [INFO] CUDA可用性: True
2025-06-03 13:12:51 [INFO] CUDA版本: 12.4
2025-06-03 13:12:51 [INFO] cuDNN版本: 90100
2025-06-03 13:12:51 [INFO] CUDA设备数量: 1
2025-06-03 13:12:51 [INFO] CUDA设备 0: NVIDIA GeForce RTX 3060 Ti
2025-06-03 13:12:51 [INFO] ===== PyTorch CUDA 检查完成 =====
2025-06-03 13:12:51 [INFO] 强制使用CUDA模式，且CUDA可用
2025-06-03 13:12:51 [INFO] 开始加载SentenceTransformer模型: paraphrase-multilingual-mpnet-base-v2
2025-06-03 13:12:51 [INFO] 尝试方法1: 直接加载模型到cuda设备
2025-06-03 13:12:51 [INFO] Load pretrained SentenceTransformer: paraphrase-multilingual-mpnet-base-v2
2025-06-03 13:12:56 [INFO] 方法1成功: 模型加载完成，使用设备: cuda
2025-06-03 13:12:56 [INFO] 成功创建嵌入函数: paraphrase-multilingual-mpnet-base-v2
2025-06-03 13:12:56 [INFO] 成功初始化嵌入函数
2025-06-03 13:12:56 [INFO] Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
2025-06-03 13:12:56 [ERROR] 获取或创建集合失败: 'str' object is not callable
2025-06-03 13:12:56 [INFO] 成功获取现有集合并手动设置嵌入函数
2025-06-03 13:12:57 [INFO] ChromaDB集合正常工作，查询返回了结果
2025-06-03 13:12:57 [INFO] 开始第一部分：商品合规性分析
