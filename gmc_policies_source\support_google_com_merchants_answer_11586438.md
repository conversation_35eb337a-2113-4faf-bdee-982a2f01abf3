[Skip to main content](https://support.google.com/merchants/answer/********?hl=en&ref_topic=********#hcfe-content)

# How to upload your products to Merchant Center

![Choose the product upload method that's right for you [hero image]](https://storage.googleapis.com/support-kms-prod/id34Uk7ng3L2CmhdR13UQebKN612cqzMp0je)

There are multiple ways to add your product data to Merchant Center. You can choose to use a combination of methods to fit your needs as a retailer, and where you keep your product information today.

This guide can help you decide which upload methods will work best for you.

Your product data sources are managed in the “Data Sources” tab, which is found in the top right section of Merchant Center under the account **Settings & tools** icon ![Tools and setting menu icon [Gear]](https://storage.googleapis.com/support-kms-prod/vyDyoYvlunmkVyVjH9CZTw60xwyZpzKoWuPC).

#### To access your sources tab:

1. In your [Merchant center account](https://merchants.google.com/), click the **Settings & tools** icon ![Tools and setting menu icon [Gear]](https://storage.googleapis.com/support-kms-prod/vyDyoYvlunmkVyVjH9CZTw60xwyZpzKoWuPC).
2. Select **Data sources** tab from the drop-down menu.

#### On this page

- [Add products automatically from your online store](https://support.google.com/merchants/answer/********?hl=en&ref_topic=********#online-store)
- [Connect your ecommerce platform to Google](https://support.google.com/merchants/answer/********?hl=en&ref_topic=********#ecommerce)
- [Add products from a file](https://support.google.com/merchants/answer/********?hl=en&ref_topic=********#sync-file)
- [Use a Google Sheets template](https://support.google.com/merchants/answer/********?hl=en&ref_topic=********#google-sheets)
- [Add products one-by-one](https://support.google.com/merchants/answer/********?hl=en&ref_topic=********#product-editor)
- [About using API method](https://support.google.com/merchants/answer/********?hl=en&ref_topic=********#About_using_API_method)
- [Next steps](https://support.google.com/merchants/answer/********?hl=en&ref_topic=********#nextsteps)

* * *

## Add products automatically from your online store

#### How it works

You can let Google automatically add any new products that are added to your online store to Merchant Center. This can be done by adding suitable information about your products in a standard format (schema.org markup) to your online store, and enabling Google to collect this information.

Google automatically finds your products from the latest product information provided in your online store and adds the information to Merchant Center. Learn more [about structured data markup for Merchant Center](https://support.google.com/merchants/answer/6069143#Getting_started).

In order for your Merchant Center to be eligible for this method of adding products, we suggest adding schema.org structured data markup to all product pages on your website. Learn how to [set up structured data for Merchant Center](https://support.google.com/merchants/answer/7331077) and [add products automatically from your online store](https://support.google.com/merchants/answer/12158480).

#### What makes this method great

- It's very simple to set up, all we need is the URL for your online store that you provided in Merchant Center.
- Your product information in Merchant Center is updated automatically. Google looks at your online store for any changes (such as price changes or product removals), and then updates the information in Merchant Center.

#### Things to note

- You can [edit product information manually](https://support.google.com/merchants/answer/12158382) in Merchant Center after it’s been automatically added, or choose to manage the same products via a different upload method.
- Once a product is managed via a non-automatic method it will no longer be updated automatically using crawled data.
- After products have been added to Merchant Center, you control where they’re visible on Google.
- This method isn't compatible with local inventory ads or free local listings.

**Note**: We're always working on ways to improve our products, and this includes experimenting with how automatically added products are managed in Merchant Center. Therefore, some merchants may see different experiences.

[Learn more](https://support.google.com/merchants/answer/12158480)

* * *

## Connect your ecommerce platform to Google

#### How it works

If you have a store that runs on a third-party platform (for example, Shopify), you can connect it to Google. All of your product information from your store (such as images, prices, and descriptions) will be automatically updated in Merchant Center.

#### What makes this method great

- It's simple to set up. Select your ecommerce platform and follow the on-screen instructions to connect the platform with Merchant Center.

#### Things to note

- After connecting your ecommerce platform to Merchant Center, it’s recommended to manage your product data using that platform. Keep in mind that changes may take 24-48 hours to reflect in Merchant Center. Product data is updated automatically (so there’s no need to maintain a separate data set for use on Google).

#### Instructions

- In your [Merchant Center account](https://merchants.google.com/), select your ecommerce platform from the ones listed in Merchant Center. You will then be presented with instructions specific to that platform type.


  - Your products will be added to Merchant Center a few minutes after completing the instructions.

[Learn more](https://support.google.com/merchants/answer/********)

* * *

## Add products from a file

#### How it works

In Merchant Center, you add a file that contains all your product details such as images, prices, descriptions, and more. Each product detail in your file should map to [our list of attributes](https://support.google.com/merchants/answer/********).

#### What makes this method great

- It allows you to use a file you already have and offers the same convenience of automatic update methods, but with more versatility.
- Produces a rich set of product data. For example, your file can be a combination of data from different sources. Learn more about [creating a product file for Merchant Center](https://support.google.com/merchants/answer/********).

#### Things to note

- The formatting of your file is very important. Incorrectly formatted or missing data can cause errors in your account. Learn more about [creating a product file for Merchant Center](https://support.google.com/merchants/answer/********).
- You can choose to do a one-time upload of a file from your computer or host your file from a URL that is synced with Merchant Center every 24 hours. Any updates that you make to the file are reflected in Merchant Center.

#### Instructions

1. Create your file by following the Merchant Center requirements and specifications.
2. Choose whether you want to upload the file or host the file from a URL. Follow the on-screen instructions for the method you choose.
   - If you choose to host the file at a URL, Google will retrieve the file from your URL every 24 hours (you will still also be able to upload a new file at any time).

[Learn more](https://support.google.com/merchants/answer/********)

* * *

## Use a Google Sheets template

#### How it works

If you're looking for a template to help you share product details, use this option. With this method, you'll maintain a spreadsheet with all your product data. Any changes you make to the information in the spreadsheet will automatically be reflected in Merchant Center.

#### What makes this method great

- Provides you with a template for your data.
- Updates automatically.

#### Things to note

- The spreadsheet must follow the template to work correctly.

#### Instructions

1. Select the “Use a Google Sheets template” option and select **Use template** button, to open the spreadsheet template.
2. Fill in the spreadsheet with your product details following the instructions included in the template.
3. After you fill in the template, select “Add products from spreadsheet” in Merchant Center. The data in the spreadsheet is used to add products in Merchant Center.
4. Your spreadsheet is synced with Merchant Center every 24 hours.

[Learn more](https://support.google.com/merchants/answer/12158053)

* * *

## Add products one-by-one

#### How it works

You will create your product data by manually entering the information for each of your products in Merchant Center.

#### What makes this method great

- It's easy to create and edit products, and you can get products live very quickly.
- You can add one or just a few products.

#### Things to note

- Offers don't expire.
- You can use this method in conjunction with other methods (for example, adding additional products to those that Google was able to extract from your website).

#### Instructions

1. After you select this method, you'll fill out all of the details about each product directly in Merchant Center.
2. After you add your products, you can edit, delete or add more at any time.
3. If you want to add multiple similar products this way, use the "Save & add another" option when saving, and the product data will be copied into a new form where you can make edits and save a new, similar product.

[Learn more](https://support.google.com/merchants/answer/********)

* * *

## About using API method

#### How it works

The [Content API for Shopping](https://developers.google.com/shopping-content/guides/quickstart#retrieve-a-service-account-key-for-authentication) allows apps to interact directly with the Merchant Center platform, vastly increasing the efficiency of managing large or complex Merchant Center accounts.

#### What makes this method great

- The Content API for Shopping can be used to manage your Merchant Center configuration, act as an input source for your product data, and provide reporting.
- If you plan to upload a large number of feeds or make frequent changes to your product data, it’s strongly recommended that you upload this directly using the [Content API](https://developers.google.com/shopping-content/v2/quickstart#retrieve-a-service-account-key-for-authentication).

[Learn more](https://support.google.com/merchants/answer/********)

* * *

## Next steps

After you've uploaded your products to Merchant Center, you can update details about the product, including specifying its availability at your online and physical stores using the “Sales channels” tab. Learn how to [assign specific products to your online and physical stores](https://support.google.com/merchants/answer/********).

Give feedback about this article

Choose a section to give feedback on

## Was this helpful?

How can we improve it?

Submit

## Need more help?

### Try these next steps:

[Post to the help community  Get answers from community members](https://support.google.com/google-ads/threads?thread_filter=(category%3Agoogle_ads_google_shopping_merchant_center)) [Contact usTell us more and we’ll help you get there](https://support.google.com/merchants/gethelp)

true

[![](https://storage.googleapis.com/support-kms-prod/brYUzW7cjhgbnCIA0RzIkWFEDE0A8RjqrZaE)](https://business.google.com/advisors/?utm_campaign=MCHC&amp;utm_medium=MC&amp;utm_source=HelpCenter&amp;utm_content=Sidepanelpromo)

[Get help with Merchant Center setup from Small Business Advisors](https://business.google.com/advisors/?utm_campaign=MCHC&amp;utm_medium=MC&amp;utm_source=HelpCenter&amp;utm_content=Sidepanelpromo)

Want to receive one-on-one guidance and tailored recommendations on how to make the most out of Merchant Center? [Try booking an appointment with Small Business Advisors](https://business.google.com/advisors/?utm_campaign=MCHC&utm_medium=MC&utm_source=HelpCenter&utm_content=Sidepanelpromo).

**Important**: This service cannot troubleshoot issues, including approving Business Profile verification, resolving product disapprovals, account warnings or suspensions, or Google Ads billing.

8660825384056082741

true

Search

Clear search

Close search

Main menu

Google apps

Search Help Center

true

true

true

[Google Help](https://support.google.com/)

[Help Center](https://support.google.com/merchants/?hl=en)

[Get to know Merchant Center](https://support.google.com/merchants/topic/********?hl=en&ref_topic=7259405,) [Business settings](https://support.google.com/merchants/topic/********?hl=en&ref_topic=7259405,) [Upload your products](https://support.google.com/merchants/topic/********?hl=en&ref_topic=7259405,) [Product data spec](https://support.google.com/merchants/topic/7259406?hl=en&ref_topic=7259405,) [Market your products](https://support.google.com/merchants/topic/********?hl=en&ref_topic=7259405,) [Add-ons and additional features](https://support.google.com/merchants/topic/********?hl=en&ref_topic=7259405,) [Understand your performance](https://support.google.com/merchants/topic/********?hl=en&ref_topic=7259405,) [Troubleshoot](https://support.google.com/merchants/topic/********?hl=en&ref_topic=7259405,) [3rd party platform integrations](https://support.google.com/merchants/topic/********?hl=en&ref_topic=7259405,)

[Google Merchant Center](https://merchants.google.com/)

[Privacy Policy](https://www.google.com/intl/en/privacy.html) [Terms of Service](https://support.google.com/merchants/answer/160173)Submit feedback

[Get started](https://support.google.com/merchants/topic/15302229?hl=en&ref_topic=********,7259405,) [I need help with set up](https://support.google.com/merchants/topic/12671531?hl=en&ref_topic=********,7259405,) [Comparison Shopping Services](https://support.google.com/merchants/topic/12652782?hl=en&ref_topic=********,7259405,) [Policies and requirements](https://support.google.com/merchants/topic/7286989?hl=en&ref_topic=********,7259405,) [Glossary](https://support.google.com/merchants/table/15620279?hl=en&ref_topic=********,7259405,)

[Manage your sales tax settings](https://support.google.com/merchants/topic/12671521?hl=en&ref_topic=********,7259405,) [Manage your shipping settings](https://support.google.com/merchants/topic/12570808?hl=en&ref_topic=********,7259405,) [Manage your business settings](https://support.google.com/merchants/topic/12564660?hl=en&ref_topic=********,7259405,) [Manage integrations](https://support.google.com/merchants/topic/12564658?hl=en&ref_topic=********,7259405,)

[Add products](https://support.google.com/merchants/topic/********?hl=en&ref_topic=********,7259405,) [Maintain your product data](https://support.google.com/merchants/topic/12672214?hl=en&ref_topic=********,7259405,) [Set up rules for your product data sources](https://support.google.com/merchants/topic/14957981?hl=en&ref_topic=********,7259405,) [Create product data sources for Performance Max campaigns](https://support.google.com/merchants/topic/14958782?hl=en&ref_topic=********,7259405,) [Use AI and AR tools to help enhance product images](https://support.google.com/merchants/topic/15189223?hl=en&ref_topic=********,7259405,) [Troubleshoot product data source issues](https://support.google.com/merchants/topic/14957024?hl=en&ref_topic=********,7259405,) [Product data specifications](https://support.google.com/merchants/topic/14963864?hl=en&ref_topic=********,7259405,) [Best practices for different product situations](https://support.google.com/merchants/topic/15712169?hl=en&ref_topic=********,7259405,)

[Ad campaigns](https://support.google.com/merchants/topic/15191542?hl=en&ref_topic=********,7259405,) [Automated discounts](https://support.google.com/merchants/topic/15303099?hl=en&ref_topic=********,7259405,) [Free listings](https://support.google.com/merchants/topic/15190028?hl=en&ref_topic=********,7259405,) [Local inventory ads and free local listings](https://support.google.com/merchants/topic/14607119?hl=en&ref_topic=********,7259405,) [Prepare for big events](https://support.google.com/merchants/topic/15710958?hl=en&ref_topic=********,7259405,) [YouTube Shopping affiliate program](https://support.google.com/merchants/topic/14813437?hl=en&ref_topic=********,7259405,) [Promotions](https://support.google.com/merchants/topic/********?hl=en&ref_topic=********,7259405,) [YouTube Store](https://support.google.com/merchants/topic/15643752?hl=en&ref_topic=********,7259405,) [Vehicle ads](https://support.google.com/merchants/topic/********?hl=en&ref_topic=********,7259405,)

[Automated discounts](https://support.google.com/merchants/topic/15303099?hl=en&ref_topic=********,7259405,) [Google Customer Reviews](https://support.google.com/merchants/topic/14628801?hl=en&ref_topic=********,7259405,) [Local inventory ads and free local listings](https://support.google.com/merchants/topic/14607119?hl=en&ref_topic=********,7259405,) [Loyalty programs](https://support.google.com/merchants/topic/15164622?hl=en&ref_topic=********,7259405,) [Product Ratings](https://support.google.com/merchants/topic/14548703?hl=en&ref_topic=********,7259405,) [Product Studio](https://support.google.com/merchants/topic/********?hl=en&ref_topic=********,7259405,) [Promotions](https://support.google.com/merchants/topic/********?hl=en&ref_topic=********,7259405,) [Store Ratings](https://support.google.com/merchants/topic/********?hl=en&ref_topic=********,7259405,) [Vehicle ads](https://support.google.com/merchants/topic/********?hl=en&ref_topic=********,7259405,)

[About issues](https://support.google.com/merchants/topic/********?hl=en&ref_topic=********,7259405,)

[Manage your account with a Shopify integration](https://support.google.com/merchants/topic/********?hl=en&ref_topic=********,7259405,)

true

true

71525

false

false

## What is the issue with this selection?

Inaccurate - doesn't match what I see in the product

Hard to understand - unclear or translation is wrong

Missing info - relevant but not comprehensive

Irrelevant - doesn’t match the title and / or my expectations

Minor errors - formatting issues, typos, and / or broken links

Other suggestions - ideas to improve the content

## Share additional info or suggestions

​

​

Do not share any personal info

Cancel

Submit

By continuing, you agree Google uses your answers, [account & system info](https://support.google.com/merchants/answer/********?hl=en&ref_topic=********#) to improve services, per our [Privacy](https://myaccount.google.com/privacypolicy?hl=en) & [Terms](https://policies.google.com/terms?hl=en).

false

false