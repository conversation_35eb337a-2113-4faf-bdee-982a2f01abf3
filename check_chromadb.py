import os
import sys
import logging
from chromadb import PersistentClient
from embedding_utils import get_embedding_function

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)

# ChromaDB数据目录
DB_DIR = "chroma_db"


def check_chromadb_collection():
    """检查ChromaDB集合中的数据"""
    logging.info("开始检查ChromaDB集合...")

    try:
        # 确保目录存在
        if not os.path.exists(DB_DIR):
            logging.error(f"ChromaDB目录不存在: {DB_DIR}")
            print(f"ChromaDB目录不存在: {DB_DIR}")
            return

        # 初始化客户端
        client = PersistentClient(path=DB_DIR)

        # 获取所有集合
        collections = client.list_collections()
        logging.info(f"找到 {len(collections)} 个集合")
        print(f"找到 {len(collections)} 个集合:")

        for i, collection_info in enumerate(collections):
            collection_name = collection_info.name
            print(f"  {i+1}. {collection_name}")

            # 获取集合
            try:
                # 使用自定义嵌入函数，自动选择最佳设备
                embedding_function = get_embedding_function(device="auto")
                collection = client.get_collection(
                    name=collection_name, embedding_function=embedding_function
                )

                # 获取集合信息
                count = collection.count()
                logging.info(f"集合 '{collection_name}' 包含 {count} 个文档")
                print(f"     包含 {count} 个文档")

                # 如果集合为空，提示用户运行policy_to_chromadb.py
                if count == 0:
                    logging.warning(
                        f"集合 '{collection_name}' 为空，需要运行policy_to_chromadb.py"
                    )
                    print(f"     警告: 集合为空，需要运行policy_to_chromadb.py")
                else:
                    # 获取一些示例文档
                    sample_size = min(5, count)
                    result = collection.peek(limit=sample_size)

                    print(f"     示例文档 ({sample_size}/{count}):")
                    for j, (doc_id, document, metadata) in enumerate(
                        zip(result["ids"], result["documents"], result["metadatas"])
                    ):
                        doc_preview = (
                            document[:100] + "..." if len(document) > 100 else document
                        )
                        print(f"       {j+1}. ID: {doc_id}")
                        print(f"          文档: {doc_preview}")
                        print(f"          元数据: {metadata}")
                        print()

                    # 尝试执行一个简单的查询
                    print("     执行测试查询:")
                    test_queries = [
                        "谷歌购物广告 商家政策要求",
                        "谷歌购物广告 退款政策要求",
                        "谷歌购物广告 隐私政策要求",
                    ]

                    for query in test_queries:
                        try:
                            result = collection.query(
                                query_texts=[query],
                                n_results=2,
                                include=["documents", "metadatas", "distances"],
                            )

                            if result["documents"] and len(result["documents"][0]) > 0:
                                print(f"       查询: '{query}'")
                                print(
                                    f"       找到 {len(result['documents'][0])} 个结果"
                                )
                                for k, (doc, meta, dist) in enumerate(
                                    zip(
                                        result["documents"][0],
                                        result["metadatas"][0],
                                        result["distances"][0],
                                    )
                                ):
                                    doc_preview = (
                                        doc[:100] + "..." if len(doc) > 100 else doc
                                    )
                                    print(f"         {k+1}. 相似度: {1-dist:.4f}")
                                    print(f"            文档: {doc_preview}")
                                    print(f"            元数据: {meta}")
                                    print()
                            else:
                                print(f"       查询: '{query}' - 未找到结果")
                        except Exception as e:
                            print(f"       查询: '{query}' - 执行失败: {str(e)}")

            except Exception as e:
                logging.error(f"获取集合 '{collection_name}' 失败: {str(e)}")
                print(f"     获取集合失败: {str(e)}")

        if not collections:
            logging.warning("未找到任何集合，需要运行policy_to_chromadb.py")
            print("未找到任何集合，需要运行policy_to_chromadb.py")

    except Exception as e:
        logging.error(f"检查ChromaDB失败: {str(e)}")
        print(f"检查ChromaDB失败: {str(e)}")


if __name__ == "__main__":
    check_chromadb_collection()
