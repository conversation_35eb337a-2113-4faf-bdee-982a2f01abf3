# 导入必要的库和模块
import requests
import json
import base64
import urllib3

# 加载配置文件
with open("config.json", "r", encoding="utf-8") as f:
    config = json.load(f)

# 从配置中获取Shopify信息
SHOP_NAME = config["shopify"]["shop_name"]
custom_domain = config["shopify"]["custom_domain"]
API_KEY = config["shopify"]["api_key"]
API_PASSWORD = config["shopify"]["api_password"]
PRODUCT_IDS_FILE = config["shopify"]["product_ids_file"]

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

print("1. 产品状态: 所有产品")
print("2. 产品状态: 激活")
print("3. 产品状态: 草稿")
status_choice = input("请输入选项：")
if status_choice == "1":
    status = "any"
elif status_choice == "2":
    status = "active"
elif status_choice == "3":
    status = "draft"


def fetch_product_ids(status=status):
    product_ids = []
    query_url = f"https://{SHOP_NAME}.myshopify.com/admin/api/2023-04/products.json?limit=250&fields=id&status={status}"
    auth_str = f"{API_KEY}:{API_PASSWORD}"
    auth_bytes = auth_str.encode("ascii")
    auth_b64_bytes = base64.b64encode(auth_bytes)
    auth_b64_str = auth_b64_bytes.decode("ascii")
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Basic {auth_b64_str}",
    }
    print(f"开始获取 {custom_domain} 产品ID (状态: {status})")
    while query_url:
        print(f"Fetching products from: {query_url}")
        try:
            response = requests.get(
                query_url, headers=headers, timeout=10, verify=False
            )
            response.raise_for_status()
            products = response.json().get("products", [])
            for product in products:
                product_ids.append(product["id"])
            link_header = response.headers.get("Link")
            if link_header:
                links = link_header.split(",")
                next_link = None
                for link in links:
                    if 'rel="next"' in link:
                        next_link = link.split(";")[0].strip("<> ")
                        break
                query_url = next_link
            else:
                query_url = None
        except requests.exceptions.RequestException as e:
            print(f"Request failed: {e}")
            break

    return product_ids


def save_product_ids_to_file(product_ids):
    with open(f"{custom_domain}_{PRODUCT_IDS_FILE}", "w") as file:
        for product_id in product_ids:
            file.write(f"{product_id}\n")


if __name__ == "__main__":
    product_ids = fetch_product_ids()
    save_product_ids_to_file(product_ids)
    print(
        f"Fetched and saved {len(product_ids)} product IDs to {custom_domain}_{PRODUCT_IDS_FILE}"
    )
