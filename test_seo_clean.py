#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试SEO清理函数
"""

import re
from html import unescape

def clean_html_for_seo_old(html_content, max_length=160):
    """
    旧版本的清理函数（有问题的版本）
    """
    if not html_content:
        return ""

    # 移除HTML标签
    clean_text = re.sub(r"<[^>]+>", "", html_content)
    # 解码HTML实体
    clean_text = unescape(clean_text)
    # 标准化空白字符
    clean_text = re.sub(r"\s+", " ", clean_text).strip()

    # 移除特殊字符和控制字符
    clean_text = re.sub(r"[\x00-\x1f\x7f-\x9f]", "", clean_text)
    # 移除多余的标点符号（问题所在）
    clean_text = re.sub(r"[^\w\s\.\,\!\?\-\(\)]", "", clean_text)
    # 再次标准化空白字符
    clean_text = re.sub(r"\s+", " ", clean_text).strip()

    # 截断到指定长度
    if len(clean_text) > max_length:
        # 在单词边界截断
        truncated = clean_text[:max_length]
        last_space = truncated.rfind(" ")
        if last_space > max_length * 0.8:  # 如果最后一个空格位置合理
            clean_text = truncated[:last_space] + "..."
        else:
            clean_text = truncated + "..."

    return clean_text

def clean_html_for_seo_new(html_content, max_length=160):
    """
    新版本的清理函数（修复后的版本）
    """
    if not html_content:
        return ""

    # 移除HTML标签
    clean_text = re.sub(r"<[^>]+>", "", html_content)
    # 解码HTML实体
    clean_text = unescape(clean_text)
    # 标准化空白字符
    clean_text = re.sub(r"\s+", " ", clean_text).strip()

    # 移除特殊字符和控制字符
    clean_text = re.sub(r"[\x00-\x1f\x7f-\x9f]", "", clean_text)
    # 保留更多有用的标点符号，包括冒号、引号、英寸符号等
    # 保留: 字母、数字、空白字符、常用标点符号
    clean_text = re.sub(r'[^\w\s\.\,\!\?\-\(\)\:\;\"\'\x22\x27\″\′\°\×\%\#\&\+\=\/]', "", clean_text)
    # 再次标准化空白字符
    clean_text = re.sub(r"\s+", " ", clean_text).strip()

    # 截断到指定长度
    if len(clean_text) > max_length:
        # 在单词边界截断
        truncated = clean_text[:max_length]
        last_space = truncated.rfind(" ")
        if last_space > max_length * 0.8:  # 如果最后一个空格位置合理
            clean_text = truncated[:last_space] + "..."
        else:
            clean_text = truncated + "..."

    return clean_text

def test_seo_cleaning():
    """测试SEO清理功能"""
    
    # 测试用的HTML内容
    test_html = """
    <p>Product Dimensions:</p>
    <p>Chandelier Body: 14.5" W x 11.75" H x 9.75" D<br>
    Backplate: 7" W x 0.75" D x 6.75" H<br>
    Weight: 12 lbs</p>
    
    <p>Specifications:</p>
    <p>Material: Metal, Glass<br>
    Available Finishes: Brass, Polished Nickel<br>
    Voltage: 110V-220V<br>
    Light Source: E12 Bulbs (Dimmable)<br>
    Color Temperature: Warm White 2700K<br>
    Number of Lights: 2<br>
    Certifications: UL, CE, SAA<br>
    UL Listed for Damp Locations</p>
    
    <p>Application: Suitable for use in living rooms, dining rooms, bedrooms, hotel hallways, and entryways.</p>
    
    <p>Warranty: 2 years.</p>
    """
    
    print("=== SEO清理功能测试 ===")
    print("\n原始HTML内容:")
    print(test_html)
    
    print("\n" + "="*60)
    print("旧版本清理结果（有问题的版本）:")
    old_result = clean_html_for_seo_old(test_html, max_length=300)
    print(f"长度: {len(old_result)}")
    print(f"内容: {old_result}")
    
    print("\n" + "="*60)
    print("新版本清理结果（修复后的版本）:")
    new_result = clean_html_for_seo_new(test_html, max_length=300)
    print(f"长度: {len(new_result)}")
    print(f"内容: {new_result}")
    
    print("\n" + "="*60)
    print("对比分析:")
    
    # 检查关键符号是否保留
    symbols_to_check = ['"', ':', 'W', 'H', 'D', 'lbs', '110V-220V', '2700K']
    
    print("\n关键符号保留情况:")
    for symbol in symbols_to_check:
        old_has = symbol in old_result
        new_has = symbol in new_result
        status = "✓" if new_has else "✗"
        change = ""
        if old_has != new_has:
            change = f" (旧版: {'有' if old_has else '无'} → 新版: {'有' if new_has else '无'})"
        print(f"  {status} '{symbol}': {'保留' if new_has else '丢失'}{change}")
    
    print(f"\n长度对比:")
    print(f"  旧版本: {len(old_result)} 字符")
    print(f"  新版本: {len(new_result)} 字符")
    print(f"  差异: {len(new_result) - len(old_result)} 字符")

if __name__ == "__main__":
    test_seo_cleaning()
