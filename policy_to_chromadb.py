import os
import re
import glob
import chromadb
import time
import uuid
from chromadb.utils import embedding_functions
from langchain.text_splitter import (
    MarkdownHeaderTextSplitter,
    RecursiveCharacterTextSplitter,
)
import hashlib

# 配置
POLICIES_DIR = "gmc_policies_source"  # 政策文件目录
CHUNK_SIZE = 800  # 分块大小
CHUNK_OVERLAP = 100  # 块重叠大小
DB_DIR = "chroma_db"  # ChromaDB数据目录


def preprocess_markdown(content):
    """预处理Markdown内容，删除多余空行和格式化问题"""
    # 删除连续空行
    content = re.sub(r"\n{3,}", "\n\n", content)
    # 确保标题和内容间有空行
    content = re.sub(r"(#{1,6}.*)\n([^#\n])", r"\1\n\n\2", content)
    return content


def get_file_metadata(file_path):
    """从文件名中提取元数据"""
    filename = os.path.basename(file_path)

    # 从文件名中提取政策类型和ID
    if "answer" in filename:
        policy_type = "answer"
        match = re.search(r"answer_(\d+)", filename)
        policy_id = match.group(1) if match else "unknown"
    elif "topic" in filename:
        policy_type = "topic"
        match = re.search(r"topic_(\d+)", filename)
        policy_id = match.group(1) if match else "unknown"
    else:
        policy_type = "other"
        policy_id = "unknown"

    # 创建源URL
    if policy_type == "answer" or policy_type == "topic":
        source_url = f"https://support.google.com/merchants/{policy_type}/{policy_id}"
    else:
        source_url = "https://support.google.com/merchants/"

    return {
        "source": filename,
        "policy_type": policy_type,
        "policy_id": policy_id,
        "url": source_url,
    }


def generate_unique_id(file_path, idx, text_preview):
    """生成唯一ID，加入时间戳和UUID确保唯一性"""
    timestamp = str(time.time())
    unique_str = f"{file_path}_{idx}_{text_preview}_{timestamp}_{uuid.uuid4()}"
    return hashlib.md5(unique_str.encode()).hexdigest()


def split_markdown_file(file_path, file_index):
    """使用层级分块和递归分块的组合方式分割文档"""
    print(f"处理文件: {file_path}")

    with open(file_path, "r", encoding="utf-8") as f:
        content = f.read()

    # 预处理Markdown内容
    content = preprocess_markdown(content)

    # 使用Markdown标题分割器先基于标题分块
    headers_to_split_on = [
        ("#", "标题1"),
        ("##", "标题2"),
        ("###", "标题3"),
        ("####", "标题4"),
    ]

    md_splitter = MarkdownHeaderTextSplitter(headers_to_split_on=headers_to_split_on)
    header_splits = md_splitter.split_text(content)

    # 获取文件元数据
    file_metadata = get_file_metadata(file_path)

    # 处理每个根据标题分割的块，如果块太大，进一步递归分割
    final_chunks = []

    for header_idx, header_chunk in enumerate(header_splits):
        chunk_text = header_chunk.page_content
        chunk_metadata = {**header_chunk.metadata, **file_metadata}

        # 如果文本块太大，进一步分割
        if len(chunk_text) > CHUNK_SIZE:
            # 使用递归字符分割器进一步分割大块
            text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=CHUNK_SIZE,
                chunk_overlap=CHUNK_OVERLAP,
                separators=["\n## ", "\n### ", "\n#### ", "\n", "。", "，", " ", ""],
            )

            smaller_chunks = text_splitter.split_text(chunk_text)

            # 为每个小块添加元数据
            for i, small_chunk in enumerate(smaller_chunks):
                # 创建唯一ID
                text_preview = small_chunk[:20] if small_chunk else ""
                chunk_id = generate_unique_id(
                    file_path, f"{file_index}_{header_idx}_{i}", text_preview
                )

                # 组合块和元数据
                final_chunks.append(
                    {
                        "id": chunk_id,
                        "text": small_chunk,
                        "metadata": {
                            **chunk_metadata,
                            "sub_chunk": i,
                            "chunk_size": len(small_chunk),
                        },
                    }
                )
        else:
            # 如果块大小合适，直接使用
            text_preview = chunk_text[:20] if chunk_text else ""
            chunk_id = generate_unique_id(
                file_path, f"{file_index}_{header_idx}", text_preview
            )
            final_chunks.append(
                {
                    "id": chunk_id,
                    "text": chunk_text,
                    "metadata": {**chunk_metadata, "chunk_size": len(chunk_text)},
                }
            )

    return final_chunks


def initialize_chroma_db():
    """初始化ChromaDB客户端和集合"""
    # 确保目录存在
    os.makedirs(DB_DIR, exist_ok=True)

    # 导入自定义嵌入函数模块
    try:
        from embedding_utils import get_embedding_function

        # 使用自定义嵌入函数，自动选择最佳设备
        embedding_function = get_embedding_function(
            model_name="paraphrase-multilingual-mpnet-base-v2",
            device="auto",  # 自动选择CPU或CUDA
        )
        print("成功初始化嵌入函数")
    except ImportError:
        print("未找到embedding_utils模块，使用默认嵌入函数")
        # 使用Sentence Transformers作为嵌入函数
        embedding_function = embedding_functions.SentenceTransformerEmbeddingFunction(
            model_name="paraphrase-multilingual-mpnet-base-v2"  # 使用多语言模型支持中文
        )

    # 创建客户端
    client = chromadb.PersistentClient(path=DB_DIR)

    # 创建或获取集合
    try:
        # 尝试删除旧集合，如果存在
        try:
            client.delete_collection(name="google_merchant_policies")
            print("已删除旧集合")
        except Exception as e:
            print(f"删除集合失败或集合不存在: {e}")

        # 创建新集合
        collection = client.create_collection(
            name="google_merchant_policies", embedding_function=embedding_function
        )
        print("创建新集合: google_merchant_policies")
    except Exception as e:
        print(f"创建集合出错: {e}")
        try:
            collection = client.get_collection(
                name="google_merchant_policies", embedding_function=embedding_function
            )
            print("使用现有集合: google_merchant_policies")
        except Exception as e2:
            print(f"获取现有集合失败: {e2}")
            raise

    return collection


def add_chunks_to_chroma(collection, chunks):
    """将分块添加到ChromaDB集合"""
    if not chunks:
        print("没有块可添加")
        return

    # 批量添加文档
    ids = [chunk["id"] for chunk in chunks]
    texts = [chunk["text"] for chunk in chunks]
    metadatas = [chunk["metadata"] for chunk in chunks]

    # 检查ID是否有重复
    if len(ids) != len(set(ids)):
        print("警告: 发现重复ID，尝试修复...")
        # 修复重复ID
        unique_ids = []
        unique_texts = []
        unique_metadatas = []

        seen_ids = set()
        for i, id in enumerate(ids):
            if id in seen_ids:
                # 生成新ID
                new_id = id + "_" + str(uuid.uuid4())
                unique_ids.append(new_id)
            else:
                seen_ids.add(id)
                unique_ids.append(id)

            unique_texts.append(texts[i])
            unique_metadatas.append(metadatas[i])

        ids = unique_ids
        texts = unique_texts
        metadatas = unique_metadatas

    collection.add(ids=ids, documents=texts, metadatas=metadatas)

    print(f"已添加 {len(chunks)} 个块到ChromaDB")


def main():
    print("开始处理政策文档并存入ChromaDB...")

    # 初始化ChromaDB
    collection = initialize_chroma_db()

    # 获取所有Markdown文件
    md_files = glob.glob(os.path.join(POLICIES_DIR, "*.md"))
    print(f"找到 {len(md_files)} 个政策文件")

    # 记录已处理的文件数和块数
    processed_files = 0
    total_chunks = 0

    # 处理每个Markdown文件
    for file_idx, file_path in enumerate(md_files):
        try:
            # 分割文件内容
            chunks = split_markdown_file(file_path, file_idx)
            total_chunks += len(chunks)

            # 将分块添加到ChromaDB
            add_chunks_to_chroma(collection, chunks)

            processed_files += 1
            print(f"进度: {processed_files}/{len(md_files)} 文件已处理")
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {e}")
            continue

    print("\n处理完成!")
    print(f"总共处理了 {processed_files} 个文件")
    print(f"创建了 {total_chunks} 个文本块")
    print(f"ChromaDB数据已保存到 {DB_DIR} 目录")


if __name__ == "__main__":
    main()
